import { VideoData } from "../types/videos";

/**
 * Mock video data for development
 * TODO: Replace with actual API integration
 */
export const mockVideosEpisodes: VideoData[] = [
  {
    id: "1",
    videoUrl:
      "https://videos.pexels.com/video-files/2795405/2795405-uhd_1440_2560_25fps.mp4",
    posterUrl:
      "https://peach.blender.org/wp-content/uploads/title_anouncement.jpg?x11217",
    profileImageUrl: "https://placehold.co/100x100/FF2D55/white?text=UserA",
    username: "User<PERSON>",
    title: "Big Buck Bunny Adventures",
    description:
      "A short film about a giant rabbit who seeks revenge on the forest creatures who bully him. This description is intentionally longer to test the show more/less functionality.",
    tags: ["animation", "comedy", "bunny"],
    initialLikes: 1234,
    initialComments: 56,
    initialShares: 78,
    initialLiked: false,
    initialSaved: true,
    seriesTitle: "The Last Of Us",
    seasonNumber: 1,
    currentEpisodeNumber: 1,
    locked: false,
  },
  {
    id: "2",
    videoUrl:
      "https://videos.pexels.com/video-files/5512609/5512609-sd_360_640_25fps.mp4",
    posterUrl: "https://download.blender.org/ED/cover.jpg",
    profileImageUrl: "https://placehold.co/100x100/007AFF/white?text=UserB",
    username: "UserB",
    title: "Elephants Dream",
    description:
      "Two strange characters explore a mysterious, complex machine.",
    tags: ["animation", "sci-fi", "blender"],
    initialLikes: 987,
    initialComments: 12,
    initialShares: 34,
    initialLiked: true,
    initialSaved: false,
    seriesTitle: "The Last Of Us",
    seasonNumber: 1,
    currentEpisodeNumber: 2,
    locked: false,
  },
  {
    id: "3",
    videoUrl:
      "https://videos.pexels.com/video-files/3205789/3205789-sd_360_640_25fps.mp4",
    posterUrl:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c0/Big_Buck_Bunny_4K.webm/640px--Big_Buck_Bunny_4K.webm.jpg",
    profileImageUrl: "https://placehold.co/100x100/34C759/white?text=UserC",
    username: "UserC",
    title: "Forest Fire Safety",
    description: "Important tips for preventing forest fires.",
    tags: ["nature", "safety", " PSA"],
    initialLikes: 500,
    initialComments: 5,
    initialShares: 10,
    initialLiked: false,
    initialSaved: false,
    seriesTitle: "The Last Of Us",
    seasonNumber: 1,
    currentEpisodeNumber: 3,
    locked: true,
  },
];
