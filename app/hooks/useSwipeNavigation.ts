import { useState, useRef, TouchEvent } from "react";

interface UseSwipeNavigationOptions {
  itemCount: number;
  minSwipeDistance?: number;
}

interface UseSwipeNavigationReturn {
  currentIndex: number;
  setCurrentIndex: React.Dispatch<React.SetStateAction<number>>;
  isDragging: boolean;
  dragOffset: number;
  handleTouchStart: (e: TouchEvent<HTMLDivElement>) => void;
  handleTouchMove: (e: TouchEvent<HTMLDivElement>) => void;
  handleTouchEnd: (e: TouchEvent<HTMLDivElement>) => void;
}

/**
 * Custom hook for handling vertical swipe navigation between items
 */
export function useSwipeNavigation({
  itemCount,
  minSwipeDistance = 50,
}: UseSwipeNavigationOptions): UseSwipeNavigationReturn {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState(0);
  const touchStartY = useRef<number | null>(null);
  console.log("currentIndex", currentIndex);
  const handleTouchStart = (e: TouchEvent<HTMLDivElement>) => {
    if (isDragging) return;

    setIsDragging(true);
    setDragOffset(0); // Reset offset at the start of a new drag
    touchStartY.current = e.targetTouches[0].clientY;
  };

  const handleTouchMove = (e: TouchEvent<HTMLDivElement>) => {
    if (!isDragging || touchStartY.current === null) return;

    const currentY = e.targetTouches[0].clientY;
    const offset = currentY - touchStartY.current;
    setDragOffset(offset);
  };

  const handleTouchEnd = (e: TouchEvent<HTMLDivElement>) => {
    if (!isDragging || touchStartY.current === null) return;

    setIsDragging(false);

    // Use changedTouches to get the final position
    const touchEndY = e.changedTouches[0].clientY;
    const distance = touchStartY.current - touchEndY; // Positive for swipe up, negative for swipe down

    // Reset drag offset for the final snap animation
    setDragOffset(0);

    const isSwipeUp = distance > minSwipeDistance;
    const isSwipeDown = distance < -minSwipeDistance;

    if (isSwipeUp) {
      // Swipe Up -> Next Item

      setCurrentIndex((prevIndex) => Math.min(prevIndex + 1, itemCount - 1));
    } else if (isSwipeDown) {
      // Swipe Down -> Previous Item

      setCurrentIndex((prevIndex) => Math.max(prevIndex - 1, 0));
    }

    // Reset touch points
    touchStartY.current = null;
  };

  return {
    currentIndex,
    setCurrentIndex,
    isDragging,
    dragOffset,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  };
}
