import { useState, useEffect } from "react";

const DESKTOP_QUERY = "(min-width: 768px)";

// Set initial state based on SSR considerations
function getInitialState(): boolean {
  // For SSR, we need to handle the case where window is not available
  if (typeof window === "undefined") {
    return true; // Default to desktop for SSR to prevent layout shift
  }

  // For client-side, check the media query immediately
  return window.matchMedia(DESKTOP_QUERY).matches;
}

export function useMediaQuery() {
  // Initialize with the correct state immediately
  const [isDesktop, setIsDesktop] = useState(getInitialState);
  const [isInitialized, setIsInitialized] = useState(
    typeof window === "undefined"
  );

  useEffect(() => {
    // Mark as initialized immediately to handle hydration properly
    setIsInitialized(true);

    const mediaQuery = window.matchMedia(DESKTOP_QUERY);
    const handleChange = () => setIsDesktop(mediaQuery.matches);

    // Initial check
    handleChange();

    // Listen for changes
    mediaQuery.addEventListener("change", handleChange);

    // Cleanup listener
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  return { isDesktop, isInitialized };
}
