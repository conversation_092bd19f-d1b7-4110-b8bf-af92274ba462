import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createBaseApi } from "~/utils/base-service";
import type { PraiseItem, CollectItem } from "~/types/index";

export function useCollect(pageNumber = 0, pageSize = 100) {
  return useQuery<CollectItem[], Error>({
    queryKey: ["collect", pageNumber, pageSize],
    queryFn: async () => {
      const response = await createBaseApi().getCollect({
        pageNumber,
        pageSize,
      });
      if (!response.ok || !response.data) {
        throw new Error(response.msg || "Error fetching collect items");
      }
      return response.data.list;
    },
  });
}

export function usePraise(pageNumber = 0, pageSize = 100) {
  return useQuery<PraiseItem[], Error>({
    queryKey: ["praise", pageNumber, pageSize],
    queryFn: async () => {
      const response = await createBaseApi().getPraise({
        pageNumber,
        pageSize,
      });
      if (!response.ok || !response.data) {
        throw new Error(response.msg || "Error fetching praise items");
      }
      return response.data.list;
    },
  });
}

// Mutation hook for updating collect/like status
export function useUpdateRel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      userId: string;
      postId: string;
      type: "collect" | "like";
      value: boolean;
    }) => {
      const response = await createBaseApi().updateRel(params);
      if (!response.ok) {
        throw new Error(response.msg || "Failed to update relation");
      }
      return response;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries based on the type
      if (variables.type === "collect") {
        queryClient.invalidateQueries({ queryKey: ["collect"] });
      } else if (variables.type === "like") {
        queryClient.invalidateQueries({ queryKey: ["praise"] });
      }
    },
  });
}
