import { useState, useEffect, useCallback, useRef } from "react";
import { createBase<PERSON><PERSON> } from "~/utils/base-service";
import { mapPostItemsToContentItems } from "~/utils/dataMappers";
import type { ContentItem, SearchPostPayload } from "~/types/index";

export interface SearchResult {
  results: ContentItem[];
  total: number;
  query: string;
  isLoading: boolean;
  error: string | null;
}

export interface UseDebounceSearchOptions {
  delay?: number; // Debounce delay in milliseconds
  minLength?: number; // Minimum query length to trigger search
  topicId?: string; // Optional topic filter
  country?: string; // Optional country filter
  collectionId?: string; // Optional collection filter
  pageSize?: number; // Optional page size
}

export function useDebounceSearch(options: UseDebounceSearchOptions = {}) {
  const {
    delay = 500,
    minLength = 2,
    topicId = "",
    country = "",
    collectionId = "",
    pageSize = 20,
  } = options;

  const [searchResult, setSearchResult] = useState<SearchResult>({
    results: [],
    total: 0,
    query: "",
    isLoading: false,
    error: null,
  });

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const performSearch = useCallback(
    async (query: string) => {
      // Cancel previous request if it exists
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      if (!query.trim() || query.length < minLength) {
        setSearchResult({
          results: [],
          total: 0,
          query: query.trim(),
          isLoading: false,
          error: null,
        });
        return;
      }

      setSearchResult((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
        query: query.trim(),
      }));

      try {
        const api = createBaseApi();
        const searchPayload: SearchPostPayload = {
          title: query.trim(),
          topicId,
          country,
          collectionId,
          pageNumber: 1,
          pageSize,
        };
        const response = await api.searchPosts(searchPayload);

        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        if (response.ok && response.data) {
          const results = mapPostItemsToContentItems(response.data.list);
          setSearchResult({
            results,
            total: response.data.total,
            query: query.trim(),
            isLoading: false,
            error: null,
          });
        } else {
          setSearchResult({
            results: [],
            total: 0,
            query: query.trim(),
            isLoading: false,
            error: "Failed to search",
          });
        }
      } catch (error) {
        // Don't set error if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        console.error("Search error:", error);
        setSearchResult({
          results: [],
          total: 0,
          query: query.trim(),
          isLoading: false,
          error: "Search failed",
        });
      }
    },
    [minLength]
  );

  const debouncedSearch = useCallback(
    (query: string) => {
      // Clear existing timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Set new timer
      debounceTimerRef.current = setTimeout(() => {
        performSearch(query);
      }, delay);
    },
    [delay, performSearch]
  );

  const search = useCallback(
    (query: string) => {
      debouncedSearch(query);
    },
    [debouncedSearch]
  );

  const clearSearch = useCallback(() => {
    // Clear timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Cancel ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setSearchResult({
      results: [],
      total: 0,
      query: "",
      isLoading: false,
      error: null,
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    searchResult,
    search,
    clearSearch,
  };
}
