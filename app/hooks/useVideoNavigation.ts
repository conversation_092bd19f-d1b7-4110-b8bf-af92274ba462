import { useNavigate, useSearchParams } from "@remix-run/react";
import { useMediaQuery } from "./useMediaQuery";

/**
 * Hook for video navigation that handles device-specific routing
 * Mobile devices navigate to the play page directly, desktop to details page
 */
export function useVideoNavigation() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { isDesktop } = useMediaQuery();

  /**
   * Navigate to a video detail or play page based on device type
   * @param videoId The ID of the video to navigate to
   * @param options Additional navigation options
   */
  const navigateToVideo = (
    videoId: string | number,
    options?: { play?: boolean }
  ) => {
    const queryParams = options?.play ? "?play=true" : "";

    // Mobile devices go to play page, desktop to detail page
    if (!isDesktop) {
      navigate(`/videos/play/${videoId}`, {
        state: {
          searchParams: searchParams.toString(),
        },
      });
    } else {
      navigate(`/videos/${videoId}${queryParams}`);
    }
  };

  /**
   * Navigate directly to video playback page
   * @param videoId The ID of the video to navigate to
   */
  const navigateToVideoPlay = (
    videoId: string | number,
    episodeId?: string
  ) => {
    navigate(
      `/videos/play/${videoId}${episodeId ? `?episodeId=${episodeId}` : ""}`
    );
  };

  return { navigateToVideo, navigateToVideoPlay };
}
