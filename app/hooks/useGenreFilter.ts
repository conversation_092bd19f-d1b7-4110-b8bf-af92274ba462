import { useState, useEffect, useCallback } from "react";
import { createBaseApi } from "~/utils/base-service";
import { mapPostItemsToContentItems } from "~/utils/dataMappers";
import type { ContentItem, SearchPostPayload, TopicItem } from "~/types/index";

export interface GenreFilterResult {
  results: ContentItem[];
  total: number;
  isLoading: boolean;
  error: string | null;
  topics: TopicItem[];
  selectedTopicId: string | null;
}

export interface UseGenreFilterOptions {
  pageSize?: number;
  country?: string;
  collectionId?: string;
}

export function useGenreFilter(options: UseGenreFilterOptions = {}) {
  const { pageSize = 20, country = "", collectionId = "" } = options;

  const [filterResult, setFilterResult] = useState<GenreFilterResult>({
    results: [],
    total: 0,
    isLoading: false,
    error: null,
    topics: [],
    selectedTopicId: null,
  });

  // Load topics on mount
  useEffect(() => {
    const loadTopics = async () => {
      try {
        const api = createBaseApi();
        const response = await api.getHomeTopic();

        if (response.ok && response.data) {
          setFilterResult((prev) => ({
            ...prev,
            topics: response.data,
          }));
        }
      } catch (error) {
        console.error("Failed to load topics:", error);
        setFilterResult((prev) => ({
          ...prev,
          error: "Failed to load topics",
        }));
      }
    };

    loadTopics();
  }, []);

  const filterByTopic = useCallback(
    async (topicId: string | null) => {
      setFilterResult((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
        selectedTopicId: topicId,
      }));

      try {
        const api = createBaseApi();
        const searchPayload: SearchPostPayload = {
          title: "", // Empty title to get all content
          topicId: topicId || "",
          country,
          collectionId,
          pageNumber: 1,
          pageSize,
        };

        const response = await api.searchPosts(searchPayload);

        if (response.ok && response.data) {
          const results = mapPostItemsToContentItems(response.data.list);
          setFilterResult((prev) => ({
            ...prev,
            results,
            total: response.data.total,
            isLoading: false,
            error: null,
          }));
        } else {
          setFilterResult((prev) => ({
            ...prev,
            results: [],
            total: 0,
            isLoading: false,
            error: "Failed to filter content",
          }));
        }
      } catch (error) {
        console.error("Filter error:", error);
        setFilterResult((prev) => ({
          ...prev,
          results: [],
          total: 0,
          isLoading: false,
          error: "Filter failed",
        }));
      }
    },
    [country, collectionId, pageSize]
  );

  const clearFilter = useCallback(() => {
    setFilterResult((prev) => ({
      ...prev,
      results: [],
      total: 0,
      selectedTopicId: null,
      isLoading: false,
      error: null,
    }));
  }, []);

  return {
    filterResult,
    filterByTopic,
    clearFilter,
  };
}
