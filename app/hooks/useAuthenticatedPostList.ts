import React from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createBaseApi } from "~/utils/base-service";
import { useUser } from "~/context/auth-context";
import type {
  PostListPayload,
  PostListResponse,
  EpisodeItem,
  BuyPostPayload,
  BuyPostResponse,
} from "~/types/index";

interface UseAuthenticatedPostListOptions {
  collectionId: string;
  enabled?: boolean; // Allow manual control of when the query runs
}

interface UseAuthenticatedPostListResult {
  episodes: EpisodeItem[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  isRefetching: boolean;
}

/**
 * React Query hook to fetch authenticated post list
 * This will fetch the episode list with user-specific data (VIP status, access rights)
 * Only runs when user is logged in
 */
export function useAuthenticatedPostList({
  collectionId,
  enabled = true,
}: UseAuthenticatedPostListOptions): UseAuthenticatedPostListResult {
  const { isLoggedIn, userInfo } = useUser();

  const { data, isLoading, isError, error, refetch, isRefetching } = useQuery({
    queryKey: ["authenticatedPostList", collectionId, userInfo?.id],
    queryFn: async (): Promise<EpisodeItem[]> => {
      if (!isLoggedIn || !userInfo) {
        throw new Error(
          "User must be logged in to fetch authenticated post list"
        );
      }

      const payload: PostListPayload = {
        collectionId,
      };

      const api = createBaseApi();
      const response: PostListResponse = await api.getAuthenticatedPostList(
        payload
      );

      if (!response.ok) {
        throw new Error(
          response.msg || "Failed to fetch authenticated post list"
        );
      }

      return response.data?.list || [];
    },
    enabled: enabled && isLoggedIn && !!userInfo && !!collectionId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: (failureCount, error) => {
      // Don't retry if user is not authenticated
      if (error.message.includes("logged in")) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
  });

  return {
    episodes: data || [],
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
    isRefetching,
  };
}

/**
 * Hook variant that automatically refetches when user logs in
 * Useful for components that need to update episode access after authentication
 */
export function useAuthenticatedPostListWithAutoRefetch({
  collectionId,
  enabled = true,
}: UseAuthenticatedPostListOptions): UseAuthenticatedPostListResult {
  const { isLoggedIn } = useUser();

  const result = useAuthenticatedPostList({
    collectionId,
    enabled,
  });

  // Auto-refetch when user logs in (if we have episodes from non-authenticated call)
  React.useEffect(() => {
    if (isLoggedIn && !result.isLoading && result.episodes.length === 0) {
      result.refetch();
    }
  }, [isLoggedIn, result.refetch, result.isLoading, result.episodes.length]);

  return result;
}

/**
 * Mutation hook for buying/unlocking posts with coins
 * Automatically invalidates the authenticated post list after successful purchase
 */
export function useBuyPost(collectionId?: string) {
  const queryClient = useQueryClient();
  const { userInfo } = useUser();

  return useMutation({
    mutationFn: async (payload: BuyPostPayload): Promise<BuyPostResponse> => {
      const api = createBaseApi();
      const response = await api.buyPost(payload);

      if (!response.ok) {
        throw new Error(response.msg || "Failed to unlock content");
      }

      return response;
    },
    onSuccess: (_, variables) => {
      // Invalidate authenticated post list to refresh episode access
      if (collectionId && userInfo) {
        queryClient.invalidateQueries({
          queryKey: ["authenticatedPostList", collectionId, userInfo.id],
        });
      }

      // Also invalidate user info to update coin balance
      queryClient.invalidateQueries({
        queryKey: ["userInfo"],
      });

      // Invalidate collect/watchlist data as the user might have unlocked content
      queryClient.invalidateQueries({
        queryKey: ["collect"],
      });
    },
    onError: (error) => {
      console.error("Failed to buy post:", error);
    },
  });
}

// Export both hooks for different use cases
export default useAuthenticatedPostList;
