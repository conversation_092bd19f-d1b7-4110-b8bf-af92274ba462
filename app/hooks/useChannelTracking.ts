import { useEffect } from "react";
import { useLocation } from "@remix-run/react";
import { trackChannelVisitIfPresent } from "~/utils/channel";

/**
 * Hook to track channel visits when URL contains channel parameter
 * This hook should be used in the root component to track on every route change
 */
export const useChannelTracking = () => {
  const location = useLocation();

  useEffect(() => {
    // Track channel visit if channel parameter is present in URL
    trackChannelVisitIfPresent();
  }, [location.pathname, location.search]); // Re-run when route or search params change
};
