import { useEffect } from "react";
import { useRevalidator } from "@remix-run/react";
import { useTranslation } from "react-i18next";

/**
 * Hook that triggers data revalidation when the language is changed
 * This ensures all API requests are refetched with the new language setting
 */
export function useLanguageChangeRevalidation() {
  const { i18n } = useTranslation();
  const revalidator = useRevalidator();

  useEffect(() => {
    // This will run every time the language changes
    const handleLanguageChanged = (lng: string) => {
      console.log(`Language changed to: ${lng}, revalidating data...`);
      // Revalidate all loader data when language changes
      revalidator.revalidate();
    };

    i18n.on("languageChanged", handleLanguageChanged);

    return () => {
      i18n.off("languageChanged", handleLanguageChanged);
    };
  }, [i18n, revalidator]);

  return null;
}
