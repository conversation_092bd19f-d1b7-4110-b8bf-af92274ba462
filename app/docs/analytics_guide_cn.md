# 视频分析事件跟踪指南

本文档简要说明了我们的分析事件跟踪系统，以帮助开发人员和产品团队了解并验证各种事件的跟踪。

## 概述

我们使用 Google Analytics 4 (GA4) 来跟踪用户与视频内容的交互。所有事件都通过 `trackEvent` 函数记录，该函数存在于 `app/utils/analytics.ts` 文件中。

为便于维护和一致性，各类事件已被组织到不同的对象中：

- `videoEvents`: 视频播放相关事件
- `uiEvents`: 用户界面交互事件
- `languageEvents`: 语言设置相关事件
- `pageEvents`: 页面浏览事件

## 如何验证事件跟踪

有两种方法可以验证事件是否正确跟踪：

1. **开发环境**：所有事件都会记录到控制台。打开浏览器的开发者工具，在控制台中查找 `[GA Event]` 前缀的日志。

2. **生产环境**：
   - 登录 Google Analytics 仪表板
   - 导航至"实时事件"或"事件"报告
   - 筛选或搜索特定事件名称（如 `video_play`、`video_share` 等）

## 跟踪的事件

以下是所有类别事件的简要说明：

### 视频交互事件 (videoEvents)

| 事件名称         | 触发时机                                     | 主要参数                                          |
| ---------------- | -------------------------------------------- | ------------------------------------------------- |
| `video_play`     | 视频开始播放时                               | video_id, label(视频标题)                         |
| `video_pause`    | 视频暂停时                                   | video_id, label(视频标题)                         |
| `video_complete` | 视频播放完成时                               | video_id, label(视频标题)                         |
| `video_progress` | 视频播放进度达到特定百分比时 (25%, 50%, 75%) | video_id, label(视频标题), value(百分比), percent |
| `video_seek`     | 用户在视频中寻找特定位置时                   | video_id, label(视频标题), from_time, to_time     |
| `video_like`     | 用户点赞视频时                               | video_id, label(视频标题)                         |
| `video_unlike`   | 用户取消点赞视频时                           | video_id, label(视频标题)                         |
| `video_share`    | 用户分享视频时                               | video_id, label(视频标题), platform(分享平台)     |
| `video_comment`  | 用户评论视频时                               | video_id, label(视频标题)                         |
| `select_episode` | 用户选择特定剧集时                           | video_id, series_title, target_episode_id         |

### 用户界面交互事件 (uiEvents)

| 事件名称                   | 触发时机             | 主要参数                                                 |
| -------------------------- | -------------------- | -------------------------------------------------------- |
| `close_video`              | 用户关闭视频播放器时 | video_id, label(视频标题)                                |
| `language_selector_opened` | 用户打开语言选择器时 | label(设备类型), current_language, current_language_name |

### 语言设置事件 (languageEvents)

| 事件名称          | 触发时机           | 主要参数                                                         |
| ----------------- | ------------------ | ---------------------------------------------------------------- |
| `change_language` | 用户切换界面语言时 | from_language, to_language, from_language_name, to_language_name |

### 导航事件 (从 videoEvents 中延伸)

| 事件名称         | 触发时机       | 主要参数                                                                  |
| ---------------- | -------------- | ------------------------------------------------------------------------- |
| `change_episode` | 用户切换剧集时 | label(系列标题), from_episode_id, to_episode_id, from_episode, to_episode |
| `change_series`  | 用户切换系列时 | from_series, to_series                                                    |

### 页面浏览事件 (pageEvents)

| 事件名称    | 触发时机           | 主要参数 |
| ----------- | ------------------ | -------- |
| `page_view` | 访问视频播放页面时 | video_id |

## 自定义参数

所有事件都接受额外的自定义参数。常见的自定义参数包括：

- `video_id`：视频的唯一标识符
- `nonInteraction`：用于标记非互动事件（默认为 false）
- `page_path`：发生事件的页面路径
- `timestamp`：事件发生的时间戳

## 开发者扩展指南

如需添加新的事件跟踪，请遵循以下步骤：

1. 在相应的事件对象中添加新方法（如 `videoEvents`, `uiEvents` 等）
2. 在新方法内部使用 `trackEvent` 函数，提供 `action`、`category` 和其他相关参数
3. 从相应的组件导入特定事件对象并调用相应方法
4. 避免在组件中直接使用 `trackEvent` 函数，始终使用预定义的事件方法

示例：

```typescript
// 在 analytics.ts 中定义新事件
export const myEvents = {
  newAction: (param1: string, param2: string) =>
    trackEvent({
      action: "new_action",
      category: "My Category",
      label: param1,
      custom_param: param2,
    }),
};

// 在组件中使用
import { myEvents } from "~/utils/analytics";
myEvents.newAction("value1", "value2");
```

这样确保所有事件跟踪保持一致并易于维护。
