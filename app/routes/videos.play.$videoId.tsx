import {
  MetaFunction,
  useParams,
  useSearchPara<PERSON>,
  useLoaderData,
  ClientLoaderFunctionArgs,
  ClientLoaderFunction,
} from "@remix-run/react";
import { LoaderFunctionArgs } from "@remix-run/node";
import { useEffect, useState } from "react";
import VideoFeed from "~/components/video-feed";
import VideoFeedDesktop from "~/components/video-feed-desktop";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import AppHeader from "~/components/AppHeader";
import { pageEvents } from "~/utils/analytics";
import { baseApi, createBaseApi } from "~/utils/base-service";
import LoadingSpinner from "~/components/ui/LoadingSpinner";
import { useAuthenticatedPostList } from "~/hooks/useAuthenticatedPostList";
import { useUser } from "~/context/auth-context";

import {
  PostDetailsResponse,
  PostDetailsData,
  EpisodeItem,
  PostListResponse,
} from "~/types/index";
import { VideoData } from "~/types/videos"; // Import VideoData
import {
  mapPostDetailsToVideoData,
  sortEpisodesByNumber,
} from "~/utils/dataMappers";
import VideoFeedV2 from "~/components/video-feed-v2";
import Cookies from "universal-cookie";
import type { SerializeFrom } from "@remix-run/node"; // Import SerializeFrom
import i18next from "~/i18next.server";

// Define the interface for the loader data
interface VideoPageLoaderData {
  videoData: VideoData | null;
  episodes: EpisodeItem[];
  error: string | null;
}

// Note: transformPostDetailsToVideoData is now imported from dataMappers as mapPostDetailsToVideoData

export const shouldRevalidate = ({
  currentParams,
  nextParams,
  currentUrl,
  nextUrl,
  defaultShouldRevalidate,
}: {
  currentParams: any;
  nextParams: any;
  currentUrl: URL;
  nextUrl: URL;
  defaultShouldRevalidate: boolean;
}) => {
  if (
    currentUrl.searchParams.get("episodeId") !==
    nextUrl.searchParams.get("episodeId")
  ) {
    return false;
  }
  return defaultShouldRevalidate;
};

// Client-side loader to replace the useEffect logic
async function getData(
  params: any,
  locale?: string
): Promise<VideoPageLoaderData> {
  const videoId = params.videoId || ""; // Ensure videoId is not undefined

  let videoData = null;
  let episodes: EpisodeItem[] = [];
  let error = null;

  if (!videoId) {
    error = "Video ID is required";
    return {
      videoData,
      episodes,
      error,
    };
  }

  try {
    // Fetch video details

    const response: PostDetailsResponse = await createBaseApi(
      locale
    ).getPostDetails(videoId);

    if (response.ok && response.data) {
      videoData = mapPostDetailsToVideoData(response.data);

      // If it's a collection, fetch episodes
      if (videoData.contentType === "collection") {
        const episodesResponse: PostListResponse = await createBaseApi(
          locale
        ).getPostList({
          collectionId: videoId,
        });

        if (
          episodesResponse.ok &&
          episodesResponse.data &&
          episodesResponse.data.list
        ) {
          // Sort episodes by title numerically
          episodes = sortEpisodesByNumber(episodesResponse.data.list);
        }
      }
    } else {
      error = response.msg || "Failed to fetch post details.";
    }
  } catch (err: any) {
    console.error("Error fetching data:", err);
    error = err.message || "An error occurred.";
  }

  return {
    videoData,
    episodes,
    error,
  };
}

export async function loader({
  params,
  request,
}: LoaderFunctionArgs): Promise<VideoPageLoaderData> {
  const locale = await i18next.getLocale(request);
  return await getData(params, locale);
}

export async function clientLoader({
  params,
  request,
}: ClientLoaderFunctionArgs): Promise<VideoPageLoaderData> {
  return await getData(params);
}

export default function VideoPlayerPage() {
  const { isDesktop, isInitialized } = useMediaQuery();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const videoId = params.videoId;
  const episodeId = searchParams.get("episodeId"); // Get episodeId from query params if it exists

  // Use data from clientLoader
  const { videoData, episodes, error } = useLoaderData<typeof clientLoader>();
  const { isLoggedIn } = useUser();

  // Use authenticated post list when user is logged in
  const { episodes: authenticatedEpisodes } = useAuthenticatedPostList({
    collectionId: videoId || "",
    enabled: isLoggedIn && !!videoId && videoData?.contentType === "collection",
  });

  // Use authenticated episodes if user is logged in and data is available, otherwise use loader data
  const currentEpisodes =
    isLoggedIn && authenticatedEpisodes.length > 0
      ? authenticatedEpisodes
      : episodes;

  const [loading, setLoading] = useState<boolean>(false);

  // Track page view - this was moved from the loader since we need window access
  useEffect(() => {
    if (typeof window !== "undefined" && videoId) {
      // Use the centralized pageEvents.videoPlayerView function
      pageEvents.videoPlayerView(videoId);
    }
  }, [videoId]);

  if (loading) {
    return (
      <>
        <AppHeader />
        <LoadingSpinner />
      </>
    );
  }

  if (error) {
    return (
      <>
        <AppHeader />
        <div>Error: {error}</div>
      </>
    );
  }

  if (!videoData) {
    // This replaces the former !postDetails check
    return (
      <>
        <AppHeader />
        <div>No video data found.</div>
      </>
    );
  }

  // Determine the currently active episode ID for proper highlighting in the UI
  const activeEpisodeId = episodeId || videoId || "";

  return (
    <>
      <AppHeader />
      {/* Only render the component once we've confirmed the device type */}
      {isDesktop ? (
        <VideoFeedDesktop videoData={videoData} episodes={currentEpisodes} />
      ) : (
        <VideoFeedV2 videoData={videoData} episodes={currentEpisodes} />
      )}
    </>
  );
}

export const meta: MetaFunction<typeof clientLoader> = (args) => {
  // Type args.data explicitly if inference is failing for the linter
  const data = args.data as SerializeFrom<VideoPageLoaderData> | undefined;
  const params = args.params;
  const location = args.location;

  if (!data || data.error) {
    return [
      { title: data?.error || "Error loading video | SnapDrama" },
      { name: "description", content: "Could not load video details." },
    ];
  }

  const { episodes, videoData } = data;

  const queryParams = new URLSearchParams(location.search);
  const currentEpisodeId = queryParams.get("episodeId");

  const currentEpisode = episodes?.find(
    (episode: EpisodeItem) => episode.id === currentEpisodeId
  );

  const currentEpisodeDescription =
    currentEpisode?.seoKeyword ||
    videoData?.description ||
    "Watch this video on SnapDrama.";
  const episodeTitleText = currentEpisode?.title
    ? ` - EP ${currentEpisode.title}`
    : ""; // Assuming "Tập" for episode, adjust if needed for i18n
  const seoKeywordsList = episodes
    ?.map((episode: EpisodeItem) => episode.seoKeyword)
    .filter(Boolean)
    .join(", ");
  const fallbackKeywords =
    videoData?.tags?.join(", ") || "short videos, drama, series";

  const title = `${videoData?.title || "Video"}${episodeTitleText} | SnapDrama`;

  return [
    { title },
    {
      name: "description",
      content: currentEpisodeDescription,
    },
    {
      name: "keywords",
      content: seoKeywordsList || fallbackKeywords,
    },
  ];
};
