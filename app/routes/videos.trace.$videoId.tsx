import { useState, useEffect } from "react";
import {
  Play,
  // Star, // Star is only used in DesktopView now
  // TODO: Add other icons if needed, like for tags if they are icons
} from "lucide-react";
import {
  useLoaderData,
  MetaFunction,
  ClientLoaderFunctionArgs,
  useNavigate,
  useParams,
  useLocation,
  useSearchParams,
} from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useTranslation } from "react-i18next";
import Cookies from "universal-cookie";

import { createBaseApi, baseApi } from "~/utils/base-service";
import type {
  PostDetailsResponse,
  PostDetailsData,
  PostListResponse,
  EpisodeItem,
} from "~/types/index";
// Button import removed as it's no longer directly used here
// BaseVideoPlayer import removed as it's no longer used for direct display
import AppHeader from "~/components/AppHeader";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import DesktopView from "~/components/video-trace/DesktopView";
import MobileView from "~/components/video-trace/MobileView";
import type { ViewProps } from "~/components/video-trace/types";
import AppHeaderTrace from "~/components/AppHeaderTrace";
import { referralEvents } from "~/utils/analytics";

import i18next from "~/i18next.server";

// --- Common getData function (simplified for this page) ---
async function getData(params: any, locale?: string) {
  const videoId = params.videoId;
  let episodes: EpisodeItem[] = []; // Initialize episodes

  if (!videoId) {
    throw new Response("Not Found", {
      status: 404,
      statusText: "Missing video ID",
    });
  }

  try {
    const response: PostDetailsResponse = await createBaseApi(
      locale
    ).getPostDetails(videoId);

    if (!response.ok || !response.data) {
      throw new Response("Not Found", {
        status: 404,
        statusText: `Video with ID ${videoId} not found`,
      });
    }

    const videoDetails = response.data;

    // If it's a collection, fetch episodes
    if (videoDetails.contentType === "collection") {
      const episodesResponse: PostListResponse = await createBaseApi(
        locale
      ).getPostList({
        collectionId: videoId,
      });

      if (
        episodesResponse.ok &&
        episodesResponse.data &&
        episodesResponse.data.list
      ) {
        episodes = episodesResponse.data.list;
      } else {
        console.warn(
          `Failed to fetch episodes for collection ID ${videoId}:`,
          episodesResponse.msg
        );
        // Optionally, handle this error more gracefully, e.g., by setting episodes to [] or an error state
      }
    }

    return { videoDetails, episodes }; // Return episodes along with videoDetails
  } catch (error) {
    console.error(`Failed to load data for video ID ${videoId}:`, error);
    if (error instanceof Response) {
      throw error;
    }
    throw new Response("Internal Server Error", {
      status: 500,
      statusText: "Failed to load video details.",
    });
  }
}

// --- Server-side loader ---
export async function loader({ params, request }: LoaderFunctionArgs) {
  const locale = await i18next.getLocale(request);
  return await getData(params, locale);
}

// --- Client-side loader ---
export async function clientLoader({
  params,
  request,
}: ClientLoaderFunctionArgs) {
  // Make sure to await and return the data, not the promise
  const data = await getData(params);
  return data; // Directly return data, Remix handles json stringification if needed client-side
}

clientLoader.hydrate = true;

// ViewProps interface removed, now imported from ~/components/video-trace/types

// ========== Desktop View Component ===========
// DesktopView component removed, now imported

// ========== Mobile View Component ===========
// MobileView component removed, now imported

export default function VideoTracePage() {
  const { t } = useTranslation();
  const { videoDetails, episodes } = useLoaderData<typeof clientLoader>();
  const [showMoreSynopsis, setShowMoreSynopsis] = useState(false);
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const { navigateToVideoPlay } = useVideoNavigation();
  const { isDesktop } = useMediaQuery();
  const [searchParams, setSearchParams] = useSearchParams();
  // Prevent rendering client-specific layout until client is initialized
  // to avoid hydration mismatch if SSR defaults to desktop.
  const [clientSideInitialized, setClientSideInitialized] = useState(false);
  useEffect(() => {
    setClientSideInitialized(true);
  }, []);

  if (!videoDetails) {
    return (
      <div className="bg-main-bg text-white min-h-screen flex items-center justify-center">
        <p>{t("video.notFound")}</p>
      </div>
    );
  }

  const posterImageUrl =
    videoDetails.picture ||
    "https://www.claudeusercontent.com/api/placeholder/1600/900"; // Fallback

  const synopsisLength = 100; // Define synopsis length for truncation
  const synopsis = videoDetails.introduce ?? t("video.noDescription");
  const displaySynopsis = showMoreSynopsis
    ? synopsis
    : synopsis.length > synopsisLength
    ? `${synopsis.substring(0, synopsisLength)}...`
    : synopsis;
  const { i18n } = useTranslation();
  const handlePlayNow = () => {
    if (params.videoId) {
      const traceId = searchParams.get("channel");

      if (traceId) {
        referralEvents.tracePlayReferral(
          params.videoId,
          traceId,
          videoDetails?.title,
          i18n.language
        );
      }

      navigate(`/videos/play/${params.videoId}${location.search}`);
    } else {
      console.warn("No videoId found in params for handlePlayNow");
    }
  };

  // Mock data based on images - replace with videoDetails fields
  const videoTitle = videoDetails.title || "The Last Of Us Season 1";
  // Assuming season and episode count might not be directly on PostDetailsData,
  // or needs to be formatted. Placeholder values:
  const seasonNumber = "1"; // Placeholder
  const totalEpisodes = episodes?.length > 0 ? episodes.length.toString() : "1"; // Use actual episode count
  const releaseYear = "2025/06/23"; // Placeholder
  const rating = "7.5"; // Placeholder
  const tags = videoDetails.topic || ["Adventure", "Action", "Young Love"];

  const viewProps: ViewProps = {
    videoDetails,
    t,
    handlePlayNow,
    posterImageUrl,
    displaySynopsis,
    showMoreSynopsis,
    setShowMoreSynopsis,
    synopsisLength,
    totalEpisodes: episodes?.length > 0 ? episodes.length : 1,
  };

  // Handle SSR and hydration: Render nothing or a placeholder until client is initialized
  // Or default to one view (e.g., desktop) on SSR if useMediaQuery handles SSR gracefully.
  // Given useMediaQuery's getInitialState, it defaults to desktop on SSR.
  // We use clientSideInitialized to ensure we don't flash the wrong content.
  if (!clientSideInitialized) {
    // You might want to render a loading spinner or a minimal placeholder here
    // to avoid layout shifts or rendering the desktop layout briefly on mobile.
    // For now, returning null until client is ready.
    // Or, if your useMediaQuery handles SSR well (like defaulting to desktop),
    // you could render based on its initial 'isDesktop' state.
    // The provided useMediaQuery defaults to desktop on SSR.
    // To ensure the client-side determined layout is shown without flicker:
    return null; // Or a loading spinner component
  }

  return (
    <div className="flex flex-col h-screen bg-main-bg">
      <AppHeaderTrace onOpenClick={handlePlayNow} />
      <div className="flex-1 overflow-y-auto">
        {isDesktop ? (
          <DesktopView {...viewProps} />
        ) : (
          <MobileView {...viewProps} />
        )}
      </div>
    </div>
  );
}

export const meta: MetaFunction<typeof clientLoader> = ({ data }) => {
  if (!data || typeof data !== "object" || !("videoDetails" in data)) {
    return [{ title: "Video Preview | SnapDrama" }];
  }
  const { videoDetails, episodes } = data as {
    videoDetails?: PostDetailsData;
    episodes?: EpisodeItem[];
  };
  return [
    { title: `${videoDetails?.title || "Video"} | SnapDrama` },
    {
      name: "description",
      content: videoDetails?.introduce || videoDetails?.title,
    },
    {
      name: "keywords",
      content: episodes?.map((episode) => episode.seoKeyword).join(","),
    },
  ];
};
