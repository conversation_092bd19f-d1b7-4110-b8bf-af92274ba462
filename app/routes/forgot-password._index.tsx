import { MetaFunction } from "@remix-run/react";
import ForgotPasswordComponent from "~/components/auth/ForgotPassword";

export const meta: MetaFunction = () => {
  return [
    { title: "Forgot Password | SnapDrama" },
    { name: "description", content: "Reset your password on SnapDrama." },
  ];
};

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen text-white flex flex-col relative overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 w-full h-full bg-cover bg-top z-0"
        style={{
          backgroundImage: "url('/images/login-bg.png')",
        }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
      </div>

      <ForgotPasswordComponent />
    </div>
  );
}
