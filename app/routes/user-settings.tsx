import {
  MetaFunction,
  Outlet,
  Link,
  useLocation,
  useNavigate,
} from "@remix-run/react";
import { User, Video, Settings, LogOut, Wallet } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import AppHeader from "~/components/AppHeader";
import { useUser } from "~/context/auth-context";
import TopupSection from "~/components/TopupSection";

export const meta: MetaFunction = () => {
  return [
    { title: "Settings | SnapDrama" },
    {
      name: "description",
      content: "Manage your account settings on SnapDrama.",
    },
  ];
};

export default function SettingsPage() {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { isLoggedIn, isLoadingAuth, logoutUser } = useUser();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoadingAuth && !isLoggedIn) {
      navigate("/login");
    }
  }, [isLoggedIn, isLoadingAuth, navigate]);

  const handleLogout = () => {
    logoutUser();
    navigate("/login");
  };

  const menuItems = [
    {
      id: "my-account",
      label: t("profile.myAccount"),
      icon: <User size={20} />,
      href: "/user-settings/my-account",
    },
    {
      id: "my-wallet",
      label: t("profile.myWallet", "My Wallet"),
      icon: <Wallet size={20} />,
      href: "/user-settings/my-wallet",
    },
    {
      id: "my-video",
      label: t("profile.myVideos"),
      icon: <Video size={20} />,
      href: "/user-settings/my-video",
    },
    {
      id: "settings",
      label: t("profile.settings"),
      icon: <Settings size={20} />,
      href: "/user-settings/settings",
    },
  ];

  // Show loading state while checking authentication
  if (isLoadingAuth) {
    return (
      <div className="min-h-screen bg-black flex flex-col">
        <AppHeader />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p>{t("common.loading", "Loading...")}</p>
          </div>
        </div>
      </div>
    );
  }

  // Don't render anything if not logged in (will redirect)
  if (!isLoggedIn) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black flex flex-col">
      <AppHeader />

      <div className="flex flex-1">
        {/* Side menu */}
        <div className="w-64 bg-neutral-900 lg:flex hidden flex-col">
          <div className="p-6 flex flex-col h-full min-h-0">
            <h2 className="text-xl font-semibold mb-8 text-white">
              {t("settings.title")}
            </h2>

            {/* Topup Section */}
            <TopupSection className="mb-6" />

            <nav className="flex-1 min-h-0">
              <ul className="space-y-2">
                {menuItems.map((item) => {
                  const isActive = location.pathname === item.href;
                  return (
                    <li key={item.id}>
                      <Link
                        to={item.href}
                        className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                          isActive
                            ? "bg-neutral-800 text-white"
                            : "text-gray-400 hover:text-white hover:bg-neutral-800"
                        }`}
                      >
                        {item.icon}
                        <span>{item.label}</span>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </nav>

            {/* Logout button at bottom */}
            <div className="mt-auto pt-6">
              <button
                onClick={handleLogout}
                className="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-800 transition-colors w-full"
              >
                <LogOut size={20} />
                <span>{t("profile.logout")}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 bg-black text-white">
          <Outlet />
        </div>
      </div>
    </div>
  );
}
