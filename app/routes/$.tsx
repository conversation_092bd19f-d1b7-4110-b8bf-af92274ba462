import { json } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { ErrorBoundary } from "~/components/ErrorBoundary";

export async function clientLoader({ request }: LoaderFunctionArgs) {
  throw json(
    { message: "Page not found" },
    { status: 404, statusText: "Not Found" }
  );
}

export default function CatchAll() {
  return null;
}

export { ErrorBoundary };
