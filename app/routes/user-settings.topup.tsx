import type { MetaFunction } from "@remix-run/node";
import { useEffect } from "react";
import { useNavigate } from "@remix-run/react";
import AppHeader from "~/components/AppHeader";
import TopUpPage from "~/components/TopUpPage";
import { useUser } from "~/context/auth-context";

export const meta: MetaFunction = () => {
  return [
    { title: "Top Up | SnapDrama" },
    {
      name: "description",
      content:
        "Top up your account with coins and VIP membership on SnapDrama.",
    },
  ];
};

export default function TopUpRoute() {
  const navigate = useNavigate();
  const { isLoggedIn, isLoadingAuth } = useUser();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoadingAuth && !isLoggedIn) {
      navigate("/login");
    }
  }, [isLoggedIn, isLoadingAuth, navigate]);

  // Show loading state while checking authentication
  if (isLoadingAuth) {
    return (
      <div className="min-h-screen bg-black flex flex-col">
        <AppHeader />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p>Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Don't render anything if not logged in (will redirect)
  if (!isLoggedIn) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black flex flex-col">
      <AppHeader />
      <div className="flex-1">
        <TopUpPage />
      </div>
    </div>
  );
}
