import { MetaFunction } from "@remix-run/react";
import RegisterComponent from "~/components/auth/Register";

export const meta: MetaFunction = () => {
  return [
    { title: "Register | SnapDrama" },
    { name: "description", content: "Create a new account on SnapDrama." },
  ];
};

export default function RegisterPage() {
  return (
    <div className="min-h-screen text-white flex flex-col relative overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 w-full h-full bg-cover bg-top z-0"
        style={{
          backgroundImage: "url('/images/login-bg.png')",
        }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
      </div>
      <RegisterComponent />
    </div>
  );
}
