import { useState } from "react";
import { MetaFunction, useLoaderData } from "@remix-run/react";
import { LoaderFunctionArgs } from "@remix-run/node";
import AppHeader from "~/components/AppHeader";
import StripePaymentForm from "~/components/payment/StripePaymentForm";
import { getStripePublishableKey } from "~/config/stripe";

// Define the interface for the loader data
interface PaymentTestLoaderData {
  stripePublishableKey: string;
}

export const meta: MetaFunction = () => {
  return [
    { title: "Test Payment | SnapDrama" },
    { name: "description", content: "Test Stripe payment integration." },
  ];
};

export async function loader({}: LoaderFunctionArgs) {
  // Get the Stripe publishable key from config based on NODE_ENV
  const stripePublishableKey = getStripePublishableKey();

  return {
    stripePublishableKey,
  };
}

export default function PaymentTestPage() {
  const { stripePublishableKey } = useLoaderData<PaymentTestLoaderData>();
  const [paymentComplete, setPaymentComplete] = useState(false);

  const handlePaymentSuccess = (paymentIntent: any) => {
    console.log("Payment successful:", paymentIntent);
    setPaymentComplete(true);
  };

  const handlePaymentError = (error: Error) => {
    console.error("Payment error:", error);
  };

  return (
    <div className="flex flex-col min-h-screen bg-main-bg text-white">
      <AppHeader />

      <main className="flex-grow container mx-auto px-4 py-8 max-w-md">
        <h1 className="text-2xl font-bold mb-6">Test Payment</h1>

        <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Payment Details</h2>

          <div className="mb-6">
            <p className="text-gray-300 mb-2">Amount:</p>
            <p className="text-xl font-bold">$10.00</p>
          </div>

          <div className="mb-6 p-4 border border-gray-600 rounded-md">
            <p className="text-center text-gray-400">
              This is a test payment page. No actual payment will be processed.
            </p>
            <p className="text-center text-gray-400 mt-2">
              You can use test card number: 4242 4242 4242 4242
            </p>
            <p className="text-center text-gray-400 mt-1">
              Any future date, any 3 digits for CVC, and any 5 digits for postal
              code.
            </p>
          </div>

          {paymentComplete ? (
            <div className="p-4 bg-green-900/50 border border-green-700 rounded-md">
              <h3 className="text-lg font-semibold text-green-200 mb-2">
                Payment Complete!
              </h3>
              <p className="text-green-300">
                Thank you for your purchase. Your subscription has been
                activated.
              </p>
            </div>
          ) : (
            <StripePaymentForm
              stripePublishableKey={stripePublishableKey}
              amount={1000} // $10.00 in cents
              currency="usd"
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          )}
        </div>
      </main>
    </div>
  );
}
