import { useState } from "react";
import { Home, Search, Download, User } from "lucide-react";
import { useLoaderData } from "@remix-run/react";
import type { JikanAnime, JikanApiPaginationResponse } from "app/types";
import type { ContentItem } from "~/types/index";
import { mapPostItemsToContentItems } from "~/utils/dataMappers";
import AppHeader from "~/components/AppHeader";
import { useTranslation } from "react-i18next";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";

// Loader function using Jikan API
export async function clientLoader() {
  try {
    // Fetch anime from Jikan API with genres and categories more relevant to browsing
    const response = await fetch(
      "https://api.jikan.moe/v4/anime?order_by=popularity&sort=desc&limit=25"
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const apiData: JikanApiPaginationResponse<JikanAnime> =
      await response.json();

    // Map Jikan data to ContentItem structure
    const allItems: ContentItem[] = mapPostItemsToContentItems(apiData.data);

    // Create different categories for browsing
    const data = {
      action: allItems.slice(0, 8),
      comedy: allItems.slice(8, 16),
      drama: allItems.slice(16, 24),
      fantasy: allItems.slice(24, 32),
      sciFi: allItems.slice(32, 40),
      romance: allItems.slice(40, 48),
    };

    return data;
  } catch (error) {
    console.error("Failed to fetch Jikan anime data:", error);
    return {
      action: [],
      comedy: [],
      drama: [],
      fantasy: [],
      sciFi: [],
      romance: [],
      error: "Failed to load data",
    };
  }
}

// Content Row Component
interface ContentRowProps {
  title: string;
  items: ContentItem[];
}

const ContentRow = ({ title, items }: ContentRowProps) => {
  const { navigateToVideo } = useVideoNavigation();
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <h3 className="text-white text-lg font-semibold mb-2 px-4">{title}</h3>
      <div className="flex overflow-x-auto space-x-2 px-4 pb-2">
        {items.map((item) => (
          <div
            key={item.id}
            className="flex-none w-32 md:w-40"
            onClick={() => navigateToVideo(item.id)}
          >
            <img
              src={item.thumbnailUrl}
              alt="Content thumbnail"
              className="rounded w-full h-auto object-cover"
            />
            <span className="text-white text-xs mt-1 truncate block text-center">
              {item.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

// Browse Category Header
const BrowseHeader = () => {
  const { t } = useTranslation();
  return (
    <div className="p-4 mb-4">
      <h1 className="text-white text-2xl font-bold">{t("nav.browse")}</h1>
      <p className="text-gray-400 mt-1">
        {t("browse.description") || "Discover content by genre and category"}
      </p>
    </div>
  );
};

// Bottom Navigation Component
interface BottomNavProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const BottomNav = ({ activeTab, setActiveTab }: BottomNavProps) => {
  const { t } = useTranslation();

  const tabs = [
    { id: "home", label: t("nav.browse"), icon: <Home size={24} /> },
    { id: "search", label: t("nav.trending"), icon: <Search size={24} /> },
    {
      id: "downloads",
      label: t("videoFeed.watchAgain"),
      icon: <Download size={24} />,
    },
    { id: "profile", label: t("nav.profile"), icon: <User size={24} /> },
  ];

  return (
    <div className="fixed pb-safe bottom-0 left-0 right-0 bg-black bg-opacity-90 flex justify-around items-center py-2 border-t border-gray-800">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => setActiveTab(tab.id)}
          className={`flex flex-col items-center px-3 py-1 ${
            activeTab === tab.id ? "text-white" : "text-gray-400"
          }`}
        >
          {tab.icon}
          <span className="text-xs mt-1">{tab.label}</span>
        </button>
      ))}
    </div>
  );
};

// Main Browse Component
export default function Browse() {
  const [activeTab, setActiveTab] = useState("home");
  const data = useLoaderData<typeof clientLoader>();
  const { t } = useTranslation();

  if (data && typeof data === "object" && "error" in data && data.error) {
    return (
      <div className="bg-black min-h-screen text-white flex items-center justify-center">
        <p>Error loading content: {data.error}</p>
      </div>
    );
  }

  const hasData = (
    d: any
  ): d is {
    action: ContentItem[];
    comedy: ContentItem[];
    drama: ContentItem[];
    fantasy: ContentItem[];
    sciFi: ContentItem[];
    romance: ContentItem[];
  } => {
    return (
      d &&
      typeof d === "object" &&
      !("error" in d) &&
      Array.isArray(d.action) &&
      Array.isArray(d.comedy) &&
      Array.isArray(d.drama) &&
      Array.isArray(d.fantasy) &&
      Array.isArray(d.sciFi) &&
      Array.isArray(d.romance)
    );
  };

  if (!hasData(data)) {
    return (
      <div className="bg-black min-h-screen text-white flex items-center justify-center">
        <p>Loading or invalid data structure...</p>
      </div>
    );
  }

  return (
    <div className="bg-black min-h-screen text-white">
      <AppHeader />

      {/* Main Content */}
      <div className="pb-16 pt-16">
        <BrowseHeader />
        <div className="mt-4">
          <ContentRow
            title={t("browse.genres.action") || "Action"}
            items={data.action}
          />
          <ContentRow
            title={t("browse.genres.comedy") || "Comedy"}
            items={data.comedy}
          />
          <ContentRow
            title={t("browse.genres.drama") || "Drama"}
            items={data.drama}
          />
          <ContentRow
            title={t("browse.genres.fantasy") || "Fantasy"}
            items={data.fantasy}
          />
          <ContentRow
            title={t("browse.genres.sciFi") || "Sci-Fi"}
            items={data.sciFi}
          />
          <ContentRow
            title={t("browse.genres.romance") || "Romance"}
            items={data.romance}
          />
        </div>
      </div>

      <BottomNav activeTab={activeTab} setActiveTab={setActiveTab} />
    </div>
  );
}
