import { useState } from "react";
import { Home, Search, Download, User, UserPlus } from "lucide-react";
import { useNavigate, useLoaderData } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import type { Jikan<PERSON>nime, JikanApiPaginationResponse } from "app/types";
import AppHeader from "~/components/AppHeader";
import { useTranslation } from "react-i18next";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";

// Define types for content items and creators
interface ContentItem {
  id: number;
  thumbnailUrl: string;
  name: string;
}

interface Creator {
  id: number;
  name: string;
  avatarUrl: string;
  isFollowing: boolean;
  contentCount: number;
}

// Loader function using Jikan API
export async function clientLoader({ request }: LoaderFunctionArgs) {
  try {
    // In a real app, this would fetch followed creators and their content from a backend API
    // For demo purposes, we're using top studios from Jikan API as "creators"
    const studiosResponse = await fetch(
      "https://api.jikan.moe/v4/producers?limit=10"
    );
    if (!studiosResponse.ok) {
      throw new Error(`HTTP error! status: ${studiosResponse.status}`);
    }
    const studiosData = await studiosResponse.json();

    // And generic anime content from Jikan
    const animeResponse = await fetch(
      "https://api.jikan.moe/v4/anime?limit=25"
    );
    if (!animeResponse.ok) {
      throw new Error(`HTTP error! status: ${animeResponse.status}`);
    }
    const animeData: JikanApiPaginationResponse<JikanAnime> =
      await animeResponse.json();

    // Simulate followed creators using studio data
    const followedCreators: Creator[] = studiosData.data
      .slice(0, 5)
      .map((studio: any, index: number) => ({
        id: studio.mal_id,
        name: studio.titles[0].title,
        avatarUrl: `https://www.claudeusercontent.com/api/placeholder/48/48?name=${encodeURIComponent(
          studio.titles[0].title
        )}`,
        isFollowing: true,
        contentCount: 10 + index * 5, // Fake content count
      }));

    // Simulate suggested creators
    const suggestedCreators: Creator[] = studiosData.data
      .slice(5, 10)
      .map((studio: any, index: number) => ({
        id: studio.mal_id,
        name: studio.titles[0].title,
        avatarUrl: `https://www.claudeusercontent.com/api/placeholder/48/48?name=${encodeURIComponent(
          studio.titles[0].title
        )}`,
        isFollowing: false,
        contentCount: 5 + index * 5, // Fake content count
      }));

    // Map anime data to ContentItem for followed content
    const allContent: ContentItem[] = animeData.data.map(
      (anime: JikanAnime) => ({
        id: anime.mal_id,
        name: anime.title,
        thumbnailUrl:
          anime.images.jpg.large_image_url ??
          anime.images.jpg.image_url ??
          `https://www.claudeusercontent.com/api/placeholder/192/108?name=${encodeURIComponent(
            anime.title
          )}`,
      })
    );

    // Distribute content into categories
    const data = {
      followedCreators,
      suggestedCreators,
      recentlyAdded: allContent.slice(0, 6),
      popular: allContent.slice(6, 12),
      newForYou: allContent.slice(12, 18),
      recommended: allContent.slice(18, 24),
    };

    return data;
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      followedCreators: [],
      suggestedCreators: [],
      recentlyAdded: [],
      popular: [],
      newForYou: [],
      recommended: [],
      error: "Failed to load data",
    };
  }
}

// Content Creator Row Component
interface CreatorRowProps {
  title: string;
  creators: Creator[];
  onFollowToggle?: (creatorId: number) => void;
}

const CreatorRow = ({ title, creators, onFollowToggle }: CreatorRowProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  if (!creators || creators.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <h3 className="text-white text-lg font-semibold mb-2 px-4">{title}</h3>
      <div className="flex overflow-x-auto space-x-4 px-4 pb-2">
        {creators.map((creator) => (
          <div
            key={creator.id}
            className="flex-none w-32"
            onClick={() => navigate(`/creator/${creator.id}`)}
          >
            <div className="flex flex-col items-center">
              <div className="relative">
                <img
                  src={creator.avatarUrl}
                  alt={creator.name}
                  className="rounded-full w-16 h-16 object-cover"
                />
                {onFollowToggle && (
                  <button
                    className={`absolute -bottom-1 -right-1 p-1 rounded-full ${
                      creator.isFollowing ? "bg-red-500" : "bg-gray-800"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      onFollowToggle(creator.id);
                    }}
                  >
                    <UserPlus size={14} className="text-white" />
                  </button>
                )}
              </div>
              <span className="text-white text-xs mt-2 text-center">
                {creator.name}
              </span>
              <span className="text-gray-400 text-xs text-center">
                {creator.contentCount} {t("following.videos")}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Content Row Component
interface ContentRowProps {
  title: string;
  items: ContentItem[];
}

const ContentRow = ({ title, items }: ContentRowProps) => {
  const { navigateToVideo } = useVideoNavigation();
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <h3 className="text-white text-lg font-semibold mb-2 px-4">{title}</h3>
      <div className="flex overflow-x-auto space-x-2 px-4 pb-2">
        {items.map((item) => (
          <div
            key={item.id}
            className="flex-none w-32 md:w-40"
            onClick={() => navigateToVideo(item.id)}
          >
            <img
              src={item.thumbnailUrl}
              alt="Content thumbnail"
              className="rounded w-full h-auto object-cover"
            />
            <span className="text-white text-xs mt-1 truncate block text-center">
              {item.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

// Following Header
const FollowingHeader = () => {
  const { t } = useTranslation();
  return (
    <div className="p-4 mb-2">
      <h1 className="text-white text-2xl font-bold">{t("nav.following")}</h1>
      <p className="text-gray-400 mt-1">
        {t("following.description") || "Content from creators you follow"}
      </p>
    </div>
  );
};

// Bottom Navigation Component
interface BottomNavProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const BottomNav = ({ activeTab, setActiveTab }: BottomNavProps) => {
  const { t } = useTranslation();

  const tabs = [
    { id: "home", label: t("nav.browse"), icon: <Home size={24} /> },
    { id: "search", label: t("nav.trending"), icon: <Search size={24} /> },
    {
      id: "downloads",
      label: t("videoFeed.watchAgain"),
      icon: <Download size={24} />,
    },
    { id: "profile", label: t("nav.profile"), icon: <User size={24} /> },
  ];

  return (
    <div className="fixed pb-safe bottom-0 left-0 right-0 bg-black bg-opacity-90 flex justify-around items-center py-2 border-t border-gray-800">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => setActiveTab(tab.id)}
          className={`flex flex-col items-center px-3 py-1 ${
            activeTab === tab.id ? "text-white" : "text-gray-400"
          }`}
        >
          {tab.icon}
          <span className="text-xs mt-1">{tab.label}</span>
        </button>
      ))}
    </div>
  );
};

// Main Following Component
export default function Following() {
  const [activeTab, setActiveTab] = useState("downloads");
  const data = useLoaderData<typeof clientLoader>();
  const { t } = useTranslation();

  // Toggle follow status (would be connected to an API in a real app)
  const handleFollowToggle = (creatorId: number) => {
    console.log(`Toggle follow for creator ${creatorId}`);
    // In a real app, this would call an API to follow/unfollow
  };

  if (data && typeof data === "object" && "error" in data && data.error) {
    return (
      <div className="bg-black min-h-screen text-white flex items-center justify-center">
        <p>Error loading content: {data.error}</p>
      </div>
    );
  }

  const hasData = (
    d: any
  ): d is {
    followedCreators: Creator[];
    suggestedCreators: Creator[];
    recentlyAdded: ContentItem[];
    popular: ContentItem[];
    newForYou: ContentItem[];
    recommended: ContentItem[];
  } => {
    return (
      d &&
      typeof d === "object" &&
      !("error" in d) &&
      Array.isArray(d.followedCreators) &&
      Array.isArray(d.suggestedCreators) &&
      Array.isArray(d.recentlyAdded) &&
      Array.isArray(d.popular) &&
      Array.isArray(d.newForYou) &&
      Array.isArray(d.recommended)
    );
  };

  if (!hasData(data)) {
    return (
      <div className="bg-black min-h-screen text-white flex items-center justify-center">
        <p>Loading or invalid data structure...</p>
      </div>
    );
  }

  return (
    <div className="bg-black min-h-screen text-white">
      <AppHeader />

      {/* Main Content */}
      <div className="pb-16 pt-16">
        <FollowingHeader />
        <div className="mt-2">
          <CreatorRow
            title={t("following.categories.creators") || "Creators You Follow"}
            creators={data.followedCreators}
          />
          <CreatorRow
            title={t("following.categories.suggested") || "Suggested Creators"}
            creators={data.suggestedCreators}
            onFollowToggle={handleFollowToggle}
          />
          <ContentRow
            title={t("following.categories.recentlyAdded") || "Recently Added"}
            items={data.recentlyAdded}
          />
          <ContentRow
            title={
              t("following.categories.popular") || "Popular From Your Creators"
            }
            items={data.popular}
          />
          <ContentRow
            title={t("following.categories.newForYou") || "New For You"}
            items={data.newForYou}
          />
          <ContentRow
            title={t("following.categories.recommended") || "Recommended"}
            items={data.recommended}
          />
        </div>
      </div>

      <BottomNav activeTab={activeTab} setActiveTab={setActiveTab} />
    </div>
  );
}
