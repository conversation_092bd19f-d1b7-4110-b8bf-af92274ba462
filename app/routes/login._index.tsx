import { MetaFunction } from "@remix-run/react";
import LoginComponent from "~/components/auth/Login";

export const meta: MetaFunction = () => {
  return [
    { title: "Login | SnapDrama" },
    { name: "description", content: "Login to SnapDrama to continue." },
  ];
};

export default function LoginPage() {
  return (
    <div className="min-h-screen text-white flex flex-col relative overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 w-full h-full bg-cover bg-top z-0"
        style={{
          backgroundImage: "url('/images/login-bg.png')",
        }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
      </div>

      <LoginComponent />
    </div>
  );
}
