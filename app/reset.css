/* --- Previous Splide CSS (keep this part) --- */
.splide-fade-wrapper {
    position: relative;
    /* Consider removing padding-left/right if arrows are to be exactly at edges or overlaying fade */
    /* padding-left: 10px; */
    /* padding-right: 10px; */
  }

  .splide-fade-wrapper::before,
  .splide-fade-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 60px; /* Adjust fade width as needed */
    z-index: 2;
    pointer-events: none;
  }

  .splide-fade-wrapper::before {
    left: 0;
    /* IMPORTANT: Adjusted to match new page background color #0D0C0F */
    background: linear-gradient(to right, #0D0C0F 20%, transparent 100%);
  }

  .splide-fade-wrapper::after {
    right: 0;
    /* IMPORTANT: Adjusted to match new page background color #0D0C0F */
    background: linear-gradient(to left, #0D0C0F 20%, transparent 100%);
  }

  .splide__pagination { /* Hide default Splide pagination if not using it */
    display: none;
  }

  /* --- Updated Splide Arrow CSS (apply these changes) --- */
  .splide__arrow {
    background: transparent !important; /* No background for the button */
    border-radius: 0 !important;      /* Remove rounded corners */
    width: auto !important;           /* Adjust width to fit SVG */
    height: auto !important;          /* Adjust height to fit SVG */
    opacity: 0.7 !important;          /* Initial opacity for visibility */
    transition: opacity 0.2s ease-in-out;
    /* Ensure arrows are on top of the fade effect */
    z-index: 10 !important;
    /* Splide's default top/transform usually centers them well, adjust if necessary */
    /* top: 50%; */
    /* transform: translateY(-50%); */
  }

  .splide__arrow:hover {
    opacity: 1 !important;
  }

  .splide__arrow svg {
    fill: white !important;     /* Chevron color */
    width: 24px !important;     /* Adjust chevron size as needed */
    height: 24px !important;    /* Adjust chevron size as needed */
  }

  /* Adjust positioning to place chevrons at the edges, potentially overlaying the fade effect */
  .splide__arrow--prev {
    left: 0px !important; /* Adjust to position correctly at the start */
  }

  .splide__arrow--next {
    right: 0px !important; /* Adjust to position correctly at the end */
  }



