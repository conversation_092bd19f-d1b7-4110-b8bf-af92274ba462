// Types based on https://api.jikan.moe/v4/anime

export interface JikanImageUrls {
  image_url: string | null;
  small_image_url?: string | null;
  large_image_url?: string | null;
}

export interface JikanImages {
  jpg: JikanImageUrls;
  webp: JikanImageUrls;
}

export interface JikanTrailerImages {
  image_url: string | null;
  small_image_url: string | null;
  medium_image_url: string | null;
  large_image_url: string | null;
  maximum_image_url: string | null;
}

export interface JikanTrailer {
  youtube_id: string | null;
  url: string | null;
  embed_url: string | null;
  images: JikanTrailerImages;
}

export interface JikanTitle {
  type: string;
  title: string;
}

export interface JikanDateRange {
  day: number | null;
  month: number | null;
  year: number | null;
}

export interface JikanAiredProp {
  from: JikanDateRange;
  to: JikanDateRange;
}

export interface JikanAired {
  from: string | null;
  to: string | null;
  prop: JikanAiredProp;
  string: string;
}

export interface JikanBroadcast {
  day: string | null;
  time: string | null;
  timezone: string | null;
  string: string | null;
}

export interface JikanMALItem {
  mal_id: number;
  type: string;
  name: string;
  url: string;
}

export interface JikanAnime {
  mal_id: number;
  url: string;
  images: JikanImages;
  trailer: JikanTrailer;
  approved: boolean;
  titles: JikanTitle[];
  title: string;
  title_english: string | null;
  title_japanese: string | null;
  title_synonyms: string[];
  type: string | null;
  source: string | null;
  episodes: number | null;
  status: string;
  airing: boolean;
  aired: JikanAired;
  duration: string;
  rating: string | null;
  score: number | null;
  scored_by: number | null;
  rank: number | null;
  popularity: number | null;
  members: number | null;
  favorites: number | null;
  synopsis: string | null;
  background: string | null;
  season: string | null;
  year: number | null;
  broadcast: JikanBroadcast;
  producers: JikanMALItem[];
  licensors: JikanMALItem[];
  studios: JikanMALItem[];
  genres: JikanMALItem[];
  explicit_genres: JikanMALItem[];
  themes: JikanMALItem[];
  demographics: JikanMALItem[];
}

// Type for the overall API response structure (Top Anime endpoint)
export interface JikanApiPagination {
  last_visible_page: number;
  has_next_page: boolean;
  current_page: number;
  items: {
    count: number;
    total: number;
    per_page: number;
  };
}

export interface JikanApiPaginationResponse<T> {
  pagination: JikanApiPagination;
  data: T[];
}

// Type for the single Anime response (Anime Details endpoint)
export interface JikanApiDetailResponse<T> {
  data: T;
}

// Custom type for general content display
export interface ContentItem {
  id: string;
  name: string;
  thumbnailUrl: string;
  description?: string; // Optional description
  genre?: string; // Optional genre
  year?: number; // Optional year
  rating?: number; // Optional rating
  duration?: string; // Optional duration (e.g., "24 min per ep")
  episodes?: number; // Optional number of episodes
}
