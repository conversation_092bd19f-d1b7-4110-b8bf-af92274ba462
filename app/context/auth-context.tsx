import {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
  ReactNode,
} from "react";
import { createBase<PERSON><PERSON>, TOKEN_KEY } from "~/utils/base-service";
import { UserInfo } from "~/types/index";

// --- Auth Context Definition ---
interface AuthContextType {
  isLoggedIn: boolean;
  userInfo: UserInfo | null;
  isLoadingAuth: boolean;
  loginUser: (token: string) => Promise<boolean>; // Return boolean for success indication
  logoutUser: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// --- AuthProvider Component ---
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [isLoadingAuth, setIsLoadingAuth] = useState(true); // Start true for initial check

  const fetchUserInfo = useCallback(async (token: string): Promise<boolean> => {
    setIsLoadingAuth(true);
    try {
      const response = await createBaseApi().getUserInfo();

      // Always ensure loading state is set to false
      setIsLoadingAuth(false);

      if (response.ok && response.data) {
        setUserInfo(response.data);
        setIsLoggedIn(true);
        return true; // Indicate success
      } else {
        localStorage.removeItem(TOKEN_KEY);
        setIsLoggedIn(false);
        setUserInfo(null);
        return false; // Indicate failure
      }
    } catch (error) {
      console.error("Failed to fetch user info:", error);
      localStorage.removeItem(TOKEN_KEY);
      setIsLoggedIn(false);
      setUserInfo(null);
      setIsLoadingAuth(false); // Ensure loading state is set to false on error
      return false; // Indicate failure
    }
  }, []);

  // Initial check for token on client-side mount
  useEffect(() => {
    const token = localStorage.getItem(TOKEN_KEY);
    if (token) {
      fetchUserInfo(token).finally(() => {
        // Ensure loading state is always set to false after initial check
        setIsLoadingAuth(false);
      });
    } else {
      setIsLoadingAuth(false);
    }
  }, [fetchUserInfo]);

  const loginUser = useCallback(
    async (token: string): Promise<boolean> => {
      localStorage.setItem(TOKEN_KEY, token);
      try {
        const success = await fetchUserInfo(token);

        return success; // Return the success status of fetching user info
      } catch (error) {
        console.error("Login error:", error);
        setIsLoadingAuth(false); // Ensure loading state is set to false on error
        return false;
      }
    },
    [fetchUserInfo]
  );

  const logoutUser = useCallback(() => {
    localStorage.removeItem(TOKEN_KEY);
    setIsLoggedIn(false);
    setUserInfo(null);
  }, []);

  // // Cross-tab sync
  // useEffect(() => {
  //   const handleStorageChange = (event: StorageEvent) => {
  //     if (event.key === "authToken") {
  //       const newToken = event.newValue;
  //       if (newToken) {
  //         setIsLoadingAuth(true);
  //         fetchUserInfo(newToken).finally(() => {
  //           setIsLoadingAuth(false);
  //         });
  //       } else {
  //         setIsLoggedIn(false);
  //         setUserInfo(null);
  //       }
  //     }
  //   };
  //   window.addEventListener("storage", handleStorageChange);
  //   return () => {
  //     window.removeEventListener("storage", handleStorageChange);
  //   };
  // }, [fetchUserInfo]);

  return (
    <AuthContext.Provider
      value={{ isLoggedIn, userInfo, isLoadingAuth, loginUser, logoutUser }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// --- useUser Hook ---
export function useUser() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useUser must be used within an AuthProvider");
  }
  return context;
}
