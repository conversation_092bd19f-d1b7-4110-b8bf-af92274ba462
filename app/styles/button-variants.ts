import { cva } from "class-variance-authority";

/**
 * Episode button styles with variants for different states
 */
export const episodeButtonStyles = cva(
  "flex items-center justify-center relative rounded px-4 py-3 text-sm font-medium transition-colors",
  {
    variants: {
      variant: {
        default: "bg-[#333333] hover:bg-opacity-80 text-white", // Normal: bg #333333
        active: "bg-[#501F22] border-2 border-[#FF0F1E] text-white", // Selected: bg #501F22, border #FF0F1E
        locked: "bg-[#333333] bg-opacity-70 text-neutral-500", // Locked: bg #333333, with 30% black overlay
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);
