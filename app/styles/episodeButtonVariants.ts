import { cva } from "class-variance-authority";

// Define the episode button style variants using class-variance-authority
export const episodeButtonStyles = cva(
  "relative flex items-center justify-center h-10 rounded text-sm font-medium",
  {
    variants: {
      variant: {
        default: "bg-zinc-800 text-white hover:bg-zinc-700",
        active: "bg-[#501F22] border-[1px] border-brand-red text-white",
        locked: "bg-zinc-800 text-zinc-600 cursor-not-allowed",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);
