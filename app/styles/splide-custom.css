/* Custom styling for Splide pagination */
.custom-pagination {
  bottom: 12px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.custom-pagination .splide__pagination__page {
  width: 8px;
  height: 8px;
  background: #666;
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.custom-pagination .splide__pagination__page.is-active {
  width: 24px;
  background: #fff;
  border-radius: 4px;
  transform: scale(1);
  opacity: 1;
} 