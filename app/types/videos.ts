// Video data interfaces
export interface VideoData {
  id: string;
  videoUrl: string;
  posterUrl?: string;
  profileImageUrl?: string;
  username?: string;
  title?: string;
  description?: string;
  tags?: string[];
  initialLikes?: number;
  initialComments?: number;
  initialShares?: number;
  initialLiked?: boolean;
  initialSaved?: boolean;
  seriesTitle?: string;
  seasonNumber?: number;
  currentEpisodeNumber?: number;
  locked?: boolean;
  contentType?: string;
}

// Episode grid item interface
export interface EpisodeGridItem {
  number: number;
  locked: boolean;
  active: boolean;
}

// Short video component props
export interface ShortVideoProps {
  videoUrl: string;
  subtitleUrl?: string;
  posterUrl?: string;
  profileImageUrl?: string;
  username?: string;
  title?: string;
  description?: string;
  tags?: string[];
  initialLikes?: number;
  initialComments?: number;
  initialShares?: number;
  initialLiked?: boolean;
  initialSaved?: boolean;
  locked?: boolean;
  isActive?: boolean; // New prop to indicate if this video is currently active
  onClose: () => void;
  onLike?: (liked: boolean, currentLikes: number) => void;
  onSave?: (saved: boolean) => void;
  onShare?: () => void;
  onComment?: () => void;
  onProfileClick?: () => void;
  onVideoEnd?: () => void;
  muted?: boolean;
}

// Locked video overlay props
export interface LockedVideoOverlayProps {
  posterUrl?: string;
  seriesTitle?: string;
  onTopUpClick?: () => void;
  onNavigateClick?: () => void; // Optional navigation callback
}

// Default props for ShortVideo component
export const defaultShortVideoProps: Partial<ShortVideoProps> = {
  profileImageUrl: "https://placehold.co/100x100", // Using placeholder service
  username: "Username",
  title: "Video Title",
  description: "Video description goes here...",
  tags: [],
  initialLikes: 0,
  initialComments: 0,
  initialShares: 0,
  initialLiked: false,
  initialSaved: false,
  locked: false,
};
