// Export all types from videos.ts
export * from "./videos";

// ContentItem interface for content items displayed in the UI
export interface ContentItem {
  id: string;
  name: string;
  thumbnailUrl: string;
  description?: string | null;
  genre?: string[] | null;
  year?: number;
  rating?: number | null;
  duration?: string;
  episodes?: number | null;
}

export interface PostDetailsData {
  topic: string[];
  id: string;
  status: string;
  vip: boolean;
  gold: number;
  title: string;
  seriesTitle: string;
  picture: string;
  introduce: string;
  playUrl: string | null;
  contentType: string;
}

// Base response type for API responses
export interface BaseResponse<T> {
  data: T;
  code: number;
  crypt: boolean;
  ok: boolean;
  msg: string | null;
  timestamp: number;
}

// Use the base response type with specific data
export type PostDetailsResponse = BaseResponse<PostDetailsData>;

// Payload for getting a list of posts/episodes in a collection
export interface PostListPayload {
  collectionId: string;
}

// Updated to match the actual structure from /post/list response
export interface EpisodeItem {
  topic: string[] | null;
  id: string;
  status: string | null;
  vip: boolean;
  gold: number;
  title: string;
  picture: string | null;
  introduce: string | null;
  seoKeyword: string | null;
  playUrl: string | null; // playUrl can be null as seen in PostDetailsData
  contentType: string; // e.g., "video"
  subtitle: string | null;
  // episodeNumber?: number; // Consider adding if titles like "1.mp4" imply order
  // duration?: string;
}

// Updated to match the actual structure from /post/list response
export interface PostListResponseData {
  list: EpisodeItem[];
  total: number;
  extra: any | null; // Can be more specific if extra's structure is known
}

// Use the base response type with specific data
export type PostListResponse = BaseResponse<PostListResponseData>;

// Home language item structure

export interface LanguageInfo {
  id: string;
  title: string;
  code: string;
  name: string;
}

// Home language response type
export type HomeLangResponse = BaseResponse<LanguageInfo[]>;

// Home index list item structure
export interface HomeIndexListItem {
  id: string;
  title: string;
  picture: string;
  bigPicture: string | null;
  introduce?: string | null;
  ratings?: number | null;
  size?: number | null; // total episodes
  topic?: string[] | null;
}

// Home index item structure
export interface HomeIndexItem {
  sortValue: number;
  type: string | null;
  id: string;
  title: string;
  country: string;
  more: boolean;
  list: HomeIndexListItem[];
}

// Home index response type
export type HomeIndexResponse = BaseResponse<HomeIndexItem[]>;

export interface SearchPostItem {
  id: string;
  title: string;
  picture: string | null;
  bigPicture: string | null;
}

export interface SearchPostListData {
  list: SearchPostItem[];
  total: number;
  extra: unknown | null;
}

export interface SearchPostListResponse
  extends BaseResponse<SearchPostListData> {}

export interface SearchPostPayload {
  title: string;
  topicId: string;
  country: string;
  collectionId: string;
  pageNumber: number;
  pageSize: number;
}

// --- Enums & Misc ---

// Type definitions for Authentication
export interface RegisterPayload {
  nickname: string;
  username: string;
  password: string;
  confirmPassword: string; // Required for API validation
  code: string;
}

export interface RegisterResponse extends BaseResponse<string> {
  data: string; // e.g., "注册成功"
}

export interface LoginPayload {
  username: string;
  password: string;
  smsCode?: string; // Optional, for 2FA or passwordless
}

export interface LoginResponse extends BaseResponse<string> {
  data: string; // This is the auth token
}

export interface SendCodePayload {
  username: string;
}

export interface SendCodeResponse extends BaseResponse<string> {
  data: string; // Success message
}

// UserInfo interface based on actual API response
export interface UserInfo {
  id: string;
  crtTime: string; // Creation time in format "YYYY-MM-DD HH:mm:ss"
  uptTime: string; // Update time in format "YYYY-MM-DD HH:mm:ss"
  username: string; // Email address used as username
  country: string | null;
  password: null; // Password is never returned for security
  nickname: string;
  gold: number; // User's gold/credits balance
  vipTitle: string | null; // VIP membership title
  vipId: string | null; // VIP membership ID
  vipTime: string | null; // VIP expiration time in format "YYYY-MM-DD HH:mm:ss"
  vip: boolean; // Whether user has active VIP membership

  // Optional fields that might be added in future API versions
  phone?: string; // Phone number (not currently in API response)
  avatar?: string; // Profile picture URL
  fullName?: string; // Full name (currently using nickname)
  dateOfBirth?: string; // Date of birth
  gender?: string; // User gender
  location?: string; // User location/address
  preferences?: UserPreferences; // User preferences object
}

// User preferences interface for settings
export interface UserPreferences {
  language?: string;
  theme?: "light" | "dark" | "auto";
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy?: {
    profileVisibility: "public" | "private" | "friends";
    showOnlineStatus: boolean;
  };
}

// VIP membership details interface
export interface VipMembership {
  id: string;
  title: string;
  description: string;
  benefits: string[];
  price: number;
  currency: string;
  duration: number; // Duration in days
  isActive: boolean;
  expiresAt: string | null;
}

export interface GetUserInfoResponse extends BaseResponse<UserInfo> {
  data: UserInfo;
}

// Account update payload types
export interface UpdatePersonalInfoPayload {
  nickname: string;
  country: string;
  phone: string;
  email: string;
  password: string;
  newPassword: string;
}

export interface UpdatePasswordPayload {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Account update response types
export interface UpdatePersonalInfoResponse extends BaseResponse<UserInfo> {
  data: UserInfo; // Updated user information
}

export interface UpdatePasswordResponse extends BaseResponse<string> {
  data: string; // Success message
}

// User profile display helpers
export interface UserProfileDisplay {
  displayName: string; // nickname or fullName
  email: string; // username
  joinDate: string; // formatted crtTime
  membershipStatus: "free" | "vip";
  membershipExpiry: string | null; // formatted vipTime
  goldBalance: number;
  isVip: boolean;
}

// Forgot Password types
export interface ForgotPasswordPayload {
  username: string;
}

export interface ForgotPasswordResponse extends BaseResponse<string> {
  data: string; // Success message
}

// Watchlist types
export interface WatchlistItem {
  id: string;
  contentId: string;
  addedAt: string; // ISO date string
  contentDetails: ContentItem;
}

export interface WatchlistResponse extends BaseResponse<WatchlistItem[]> {
  data: WatchlistItem[];
}

export interface AddToWatchlistPayload {
  contentId: string;
}

export interface AddToWatchlistResponse extends BaseResponse<string> {
  data: string; // Success message
}

export interface RemoveFromWatchlistPayload {
  contentId: string;
}

export interface RemoveFromWatchlistResponse extends BaseResponse<string> {
  data: string; // Success message
}

export interface ResetPasswordPayload {
  username: string;
  password: string;
  confirmPassword: string;
  code: string;
}

// Post interaction types (like/collect)
export interface UpdateRelPayload {
  userId: string;
  postId: string;
  type: "collect" | "like";
  value: boolean;
}

export interface UpdateRelResponse extends BaseResponse<string> {
  data: string; // Success message
}

export interface ResetPasswordResponse extends BaseResponse<string> {
  data: string; // Success message
}

// Pagination payload for user content endpoints
export interface PaginationPayload {
  pageNumber: number;
  pageSize: number;
}

// Praise/Liked content types
export interface PraiseItem {
  id: string;
  title: string;
  picture: string | null;
  // Add other fields as needed based on actual API response
  topic?: string[] | null;
  status?: string | null;
  vip?: boolean;
  gold?: number;
  introduce?: string | null;
  contentType?: string;
}

export interface PraiseResponseData {
  list: PraiseItem[];
  total: number;
  extra: any | null;
}

export interface PraiseResponse extends BaseResponse<PraiseResponseData> {
  data: PraiseResponseData;
}

// Collect content types (same structure as Praise)
export interface CollectItem {
  id: string;
  title: string;
  picture: string | null;
  // Add other fields as needed based on actual API response
  topic?: string[] | null;
  status?: string | null;
  vip?: boolean;
  gold?: number;
  introduce?: string | null;
  contentType?: string;
}

export interface CollectResponseData {
  list: CollectItem[];
  total: number;
  extra: any | null;
}

export interface CollectResponse extends BaseResponse<CollectResponseData> {
  data: CollectResponseData;
}

// Topic types for /home/<USER>
export interface TopicItem {
  id: string;
  crtTime: string;
  uptTime: string;
  title: string;
}

export interface TopicResponse extends BaseResponse<TopicItem[]> {
  data: TopicItem[];
}

// Payment config types for /wallet/payConfig endpoint
export interface GoldPackage {
  id: string;
  crtTime: string | null;
  uptTime: string | null;
  title: string;
  value: number; // Number of coins
  reward: number; // Bonus coins
  price: number; // Price in currency
}

export interface MembershipPackage {
  id: string;
  crtTime: string | null;
  uptTime: string | null;
  circleType: string; // "week", "month", "year", etc.
  price: number;
  oldPrice: number | null;
  title: string;
  introduce: string;
}

export interface PayConfigData {
  golds: GoldPackage[];
  memberships: MembershipPackage[];
  payMethods: string[]; // ["paypal", "credit_card"]
}

export interface PayConfigResponse extends BaseResponse<PayConfigData> {
  data: PayConfigData;
}

// Wallet pay types for /wallet/pay endpoint
export interface WalletPayPayload {
  userId: string;
  goldId: string; // Gold package ID for coin purchases
  memberShip: string; // Membership package ID for VIP purchases
  payMethod: string; // Payment method (e.g., "credit_card")
}

export interface WalletPayResponse extends BaseResponse<string> {
  data: string; // Stripe payment intent client secret
}

// Buy VIP types for /wallet/buyVip endpoint
export interface BuyVipPayload {
  id: string; // VIP package ID
}

export interface BuyVipResponse extends BaseResponse<any> {
  data: any; // Response structure to be defined based on actual API response
}

// Buy post types for unlocking content with coins
export interface BuyPostPayload {
  id: string; // Video/Post ID to unlock
}

export interface BuyPostResponse extends BaseResponse<any> {
  data: any; // Response structure to be defined based on actual API response
}

// Wallet events types for /wallet/events endpoint
export interface WalletEvent {
  id: string;
  crtTime: string; // Creation time "YYYY-MM-DD HH:mm:ss"
  uptTime: string; // Update time "YYYY-MM-DD HH:mm:ss"
  userId: string;
  money: number; // Negative for expenses, positive for income
  tag: string; // Transaction tag like "购买会员"
  remark: string; // Transaction description like "购买会员:90天会员"
}

export interface WalletEventsPayload extends PaginationPayload {
  // Additional filters can be added here if needed
  status?: string;
  type?: string;
}

export interface WalletEventsResponseData {
  list: WalletEvent[];
  total: number;
  extra: any | null;
}

export interface WalletEventsResponse
  extends BaseResponse<WalletEventsResponseData> {
  data: WalletEventsResponseData;
}

//     getAnimeById: (id: number) => api.get<{ data: JikanAnime }>(`/anime/${id}`),
// ... existing code ...
