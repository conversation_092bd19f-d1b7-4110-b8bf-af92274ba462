declare module "@splidejs/react-splide" {
  import { ComponentType, ReactNode, HTMLAttributes } from "react";

  export interface SplideProps extends HTMLAttributes<HTMLDivElement> {
    options?: any;
    hasTrack?: boolean;
    transition?: string;
    extensions?: any;
    ref?: any;
    children?: ReactNode;
    "aria-label"?: string;
  }

  export interface SplideTrackProps extends HTMLAttributes<HTMLDivElement> {
    children?: ReactNode;
  }

  export interface SplideSlideProps extends HTMLAttributes<HTMLDivElement> {
    children?: ReactNode;
  }

  export const Splide: ComponentType<SplideProps>;
  export const SplideTrack: ComponentType<SplideTrackProps>;
  export const SplideSlide: ComponentType<SplideSlideProps>;
}

declare module "@splidejs/react-splide/css";
