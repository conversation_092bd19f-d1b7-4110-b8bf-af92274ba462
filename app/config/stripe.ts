// Stripe configuration based on environment
const stripeConfig = {
  // dev: {
  //   publishableKey:
  //     "pk_test_51RR73f4cIGELziOfniTDtk59YinfawcVmsl4nZBm5sj62IjyPn3E3TcNO0gF1Ct6xzAAAeFv6Fe9wR5pczuWJnfp003vsrnyRO",
  // },
  dev: {
    publishableKey:
      "pk_live_51RR73YGOsIo0cglahfrbuvZwjZGZUG6WUGUb5juGgahRhwcDH03OlyhjAgiNfvPJsymzfWQioiLZmKuYnlDr4MNB00lGXx8Whz",
  },

  production: {
    publishableKey:
      "pk_live_51RR73YGOsIo0cglahfrbuvZwjZGZUG6WUGUb5juGgahRhwcDH03OlyhjAgiNfvPJsymzfWQioiLZmKuYnlDr4MNB00lGXx8Whz", // TODO: Replace with actual production Stripe publishable key
  },
};

// Get current environment from NODE_ENV, default to 'dev'
const getEnvironment = (): "dev" | "production" => {
  const nodeEnv = process.env.NODE_ENV || "dev";
  return nodeEnv === "production" ? "production" : "dev";
};

// Get Stripe publishable key for current environment
export const getStripePublishableKey = (): string => {
  const env = getEnvironment();
  return stripeConfig[env].publishableKey;
};

// Export environment for other uses
export const getCurrentEnvironment = getEnvironment;

// Export the full config if needed
export default stripeConfig;
