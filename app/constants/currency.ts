// Global currency constants for the application
export const CURRENCY = {
  CODE: "MYR",
  SYMBOL: "RM",
  NAME: "Malaysian Ringgit",
} as const;

// Exchange rates and pricing
export const PRICING = {
  COIN_TO_CURRENCY_RATE: 0.14, // 1 coin = 0.14 MYR (fallback)
  VIP_MULTIPLIER: 1.2, // VIP pricing multiplier
  UNLOCK_EPISODE_COST: 20, // Cost in coins to unlock subsequent episodes
} as const;

// Coin pricing mapping based on design
export const COIN_PRICING: Record<string, number> = {
  "30": 4.23,
  "50": 21.29,
  "70": 42.61,
  "100": 4.23,
  "150": 21.29,
  "250": 42.61,
  "350": 85.26,
  "500": 21.29,
  "1000": 42.61,
  "2000": 85.26,
} as const;

// Currency formatting helper
export const formatCurrency = (amount: number, showSymbol = false): string => {
  const formatted = amount.toFixed(2);
  return showSymbol
    ? `${CURRENCY.SYMBOL} ${formatted}`
    : `${CURRENCY.CODE} ${formatted}`;
};
