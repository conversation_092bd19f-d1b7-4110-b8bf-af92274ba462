import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import TopUpContent from "./TopUpContent";
import { PRICING } from "~/constants/currency";

interface TopUpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  subtitle?: string;
}

export default function TopUpDialog({
  open,
  onOpenChange,
  title = "Unlock subsequent episodes",
  subtitle = `Price : ${PRICING.UNLOCK_EPISODE_COST} Coins to unlock this subsequent episodes`,
}: TopUpDialogProps) {
  // Add handler to stop propagation of touch events and prevent default behavior
  const handleDialogInteraction = (e: React.TouchEvent | React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
  };

  // Specific handler for the dialog content area to prevent swipe navigation while allowing scrolling
  const handleContentInteraction = (e: React.TouchEvent | React.MouseEvent) => {
    const target = e.target as Element;

    // Check if the event is coming from a nested dialog (LoginDialog, RegisterDialog, etc.)
    // These dialogs have higher z-index and should not be interfered with
    const isFromNestedDialog =
      target.closest('[class*="z-[7"]') || target.closest('[class*="z-[8"]');

    // Only prevent events if they're not from a nested dialog
    if (!isFromNestedDialog) {
      // Stop event propagation to prevent video feed swipe navigation
      e.stopPropagation();
      // Don't prevent default for touch events to allow scrolling within the dialog
      // Only prevent default for mouse events if needed
      if (e.type.startsWith("mouse")) {
        e.preventDefault();
      }
    }
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay
          className="fixed inset-0 bg-black/70 z-[60] data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut"
          // style={{ touchAction: "none" }}
        />
        <Dialog.Content
          className="fixed inset-0 z-[61] overflow-y-auto outline-none"
          style={{ touchAction: "pan-y" }}
          onTouchStart={handleDialogInteraction}
          onTouchMove={handleDialogInteraction}
          onTouchEnd={handleDialogInteraction}
        >
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="bg-[#1F2937] rounded-2xl shadow-2xl max-h-[90vh] overflow-y-auto text-white relative">
              {/* Close button for dialog */}
              <Dialog.Close className="absolute top-4 right-4 text-gray-400 hover:text-white z-10">
                <X size={24} />
              </Dialog.Close>

              {/* TopUp Content with event handling wrapper */}
              <div
                onTouchStart={handleContentInteraction}
                onTouchMove={handleContentInteraction}
                onTouchEnd={handleContentInteraction}
                onMouseDown={handleContentInteraction}
                onMouseMove={handleContentInteraction}
                onMouseUp={handleContentInteraction}
              >
                <TopUpContent title={title} subtitle={subtitle} isInDialog />
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
