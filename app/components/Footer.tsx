import { Link } from "@remix-run/react";
import { Instagram, Facebook, Twitter, Youtube } from "lucide-react"; // Using Youtube as a placeholder for G

const AppLogo = () => (
  <div className="flex items-center space-x-2">
    {/* Replace with your actual logo SVG or Image component */}
    <img src="/logo-horizontal.png" alt="SnapDrama Logo" className="w-60" />
  </div>
);

interface FooterLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
}

const FooterLink: React.FC<FooterLinkProps> = ({ to, children, className }) => (
  <Link
    to={to}
    className={`text-gray-400 hover:text-white transition-colors ${
      className || ""
    }`}
  >
    {children}
  </Link>
);

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="hidden md:block bg-main-bg text-gray-300 py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Column 1: Logo and Text */}
          <div className="space-y-4">
            <AppLogo />
            <p className="text-sm">
              This platform is a shared space. All resources are freely uploaded
              by users. If you encounter any copyright infringement, please
              contact us for removal.
            </p>
          </div>

          {/* Column 2: Discover */}
          <div>
            <h3 className="font-semibold text-white mb-4">Discover</h3>
            <ul className="space-y-2">
              <li>
                <FooterLink to="/">Home</FooterLink>
              </li>
              <li>
                <FooterLink to="/genre">Genre</FooterLink>
              </li>
              <li>
                <FooterLink to="/popular">Popular</FooterLink>
              </li>
              <li>
                <FooterLink to="/watchlist">My Watchlist</FooterLink>
              </li>
            </ul>
          </div>

          {/* Column 3: About */}
          <div>
            <h3 className="font-semibold text-white mb-4">About</h3>
            <ul className="space-y-2">
              <li>
                <FooterLink to="/contact">Contact Us</FooterLink>
              </li>
              <li>
                <FooterLink to="/feedback">Feedback</FooterLink>
              </li>
            </ul>
          </div>

          {/* Column 4: Download QR Codes */}
          <div>
            <h3 className="font-semibold text-white mb-4">Download</h3>
            <div className="flex space-x-4">
              <div className="text-center">
                {/* Replace with actual QR code images */}
                <div className="w-24 h-24 bg-gray-700 flex items-center justify-center rounded mb-1">
                  <span className="text-xs">QR Apple</span>
                </div>
                <p className="text-xs">Apple</p>
              </div>
              <div className="text-center">
                <div className="w-24 h-24 bg-gray-700 flex items-center justify-center rounded mb-1">
                  <span className="text-xs">QR Android</span>
                </div>
                <p className="text-xs">Android</p>
              </div>
            </div>
          </div>
        </div>

        <hr className="border-gray-700 my-8" />

        <div className="flex flex-col md:flex-row justify-between items-center text-sm">
          <p className="mb-4 md:mb-0">
            &copy;SnapDrama {currentYear}
            <FooterLink to="/privacy-policy" className="ml-4">
              Privacy policy
            </FooterLink>
            <FooterLink to="/terms-of-service" className="ml-4">
              Term of service
            </FooterLink>
            <FooterLink to="/disclaimer" className="ml-4">
              Disclaimer
            </FooterLink>
            <FooterLink to="/language" className="ml-4">
              Language
            </FooterLink>
          </p>
          <div className="flex space-x-4">
            <a href="#" className="text-gray-400 hover:text-white">
              <Instagram size={20} />
            </a>
            <a href="#" className="text-gray-400 hover:text-white">
              <Facebook size={20} />
            </a>
            <a href="#" className="text-gray-400 hover:text-white">
              <Twitter size={20} />
            </a>
            <a href="#" className="text-gray-400 hover:text-white">
              <Youtube size={20} />
            </a>{" "}
            {/* G icon replacement */}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
