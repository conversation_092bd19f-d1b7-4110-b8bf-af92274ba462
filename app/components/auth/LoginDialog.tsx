import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import LoginComponent from "./Login";
import { useState } from "react";
import ForgotPasswordDialog from "./ForgotPasswordDialog";

interface LoginDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOpenRegister?: () => void;
}

export default function LoginDialog({
  open,
  onOpenChange,
  onOpenRegister,
}: LoginDialogProps) {
  const [forgotPasswordOpen, setForgotPasswordOpen] = useState(false);

  const handleOpenForgotPassword = () => {
    onOpenChange(false); // Close login dialog
    setForgotPasswordOpen(true); // Open forgot password dialog
  };

  const handleForgotPasswordLogin = () => {
    setForgotPasswordOpen(false); // Close forgot password dialog
    onOpenChange(true); // Reopen login dialog
  };
  return (
    <>
      <Dialog.Root open={open} onOpenChange={onOpenChange}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/70 z-[70] data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut" />
          <Dialog.Content className="fixed inset-0 z-[71] overflow-y-auto outline-none">
            <div className="flex items-center justify-center min-h-screen p-4">
              <div className="w-full max-w-md bg-[#1F2937] rounded-2xl shadow-2xl p-6 sm:p-8 space-y-6 text-white relative">
                {/* Close button for dialog */}
                <Dialog.Close className="absolute top-4 right-4 text-gray-400 hover:text-white">
                  <X size={24} />
                </Dialog.Close>

                {/* Login Component without dialog-specific styling */}
                <LoginComponent
                  onOpenRegister={onOpenRegister}
                  onOpenForgotPassword={handleOpenForgotPassword}
                  initialStep="passwordEntry"
                />
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>

      {/* Forgot Password Dialog */}
      <ForgotPasswordDialog
        open={forgotPasswordOpen}
        onOpenChange={setForgotPasswordOpen}
        onOpenLogin={handleForgotPasswordLogin}
      />
    </>
  );
}
