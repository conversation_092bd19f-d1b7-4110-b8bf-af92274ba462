import React, { useState, useRef, ChangeEvent, KeyboardEvent } from "react";

interface OtpInputProps {
  length?: number;
  onChange: (otp: string) => void;
  disabled?: boolean;
}

export default function OtpInput({
  length = 4,
  onChange,
  disabled,
}: OtpInputProps) {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (element: HTMLInputElement, index: number) => {
    const value = element.value;
    if (/^[0-9]$/.test(value) || value === "") {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);
      onChange(newOtp.join(""));

      // Focus next input
      if (value !== "" && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (
    event: KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (event.key === "Backspace" && otp[index] === "" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <div className="flex justify-center space-x-2 sm:space-x-3">
      {otp.map((data, index) => (
        <input
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={1}
          className="w-12 h-12 sm:w-14 sm:h-14 text-center text-lg sm:text-xl font-medium bg-[#2D3748] border border-gray-600 rounded-lg focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-500 disabled:opacity-50"
          value={data}
          onChange={(e: ChangeEvent<HTMLInputElement>) =>
            handleChange(e.target, index)
          }
          onKeyDown={(e: KeyboardEvent<HTMLInputElement>) =>
            handleKeyDown(e, index)
          }
          onFocus={(e) => e.target.select()}
          disabled={disabled}
        />
      ))}
    </div>
  );
}
