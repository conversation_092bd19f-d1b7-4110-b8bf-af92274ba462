import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import RegisterComponent from "./Register";

interface RegisterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOpenLogin?: () => void;
}

export default function RegisterDialog({
  open,
  onOpenChange,
  onOpenLogin,
}: RegisterDialogProps) {
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 z-[70] data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut" />
        <Dialog.Content className="fixed inset-0 z-[71] overflow-y-auto outline-none">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="w-full max-w-md bg-[#1F2937] rounded-2xl shadow-2xl max-h-[90vh] overflow-y-auto text-white relative">
              {/* Close button for dialog */}
              <Dialog.Close className="absolute top-4 right-4 text-gray-400 hover:text-white z-10">
                <X size={24} />
              </Dialog.Close>

              {/* Register Component without dialog-specific styling */}
              <RegisterComponent onOpenLogin={onOpenLogin} />
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
