import { Link, useNavigate } from "@remix-run/react";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "~/components/ui/Button";
import { Input } from "~/components/ui/Input";
import { createBase<PERSON><PERSON> } from "~/utils/base-service";
import { ForgotPasswordPayload, ResetPasswordPayload } from "~/types/index";
import { useForm, SubmitHandler } from "react-hook-form";
import { useTranslation } from "react-i18next";

interface ForgotPasswordComponentProps {
  onOpenLogin?: () => void;
}

// Define the possible steps for forgot password
type ForgotPasswordStep = "emailEntry" | "passwordReset";

interface EmailFormInputs {
  username: string;
}

interface ResetFormInputs {
  password: string;
  confirmPassword: string;
  code: string;
}

export default function ForgotPasswordComponent(
  props: ForgotPasswordComponentProps
) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const api = createBaseApi();
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [currentStep, setCurrentStep] =
    useState<ForgotPasswordStep>("emailEntry");
  const [isLoading, setIsLoading] = useState(false);
  const [userEmail, setUserEmail] = useState("");

  const {
    register: registerEmail,
    handleSubmit: handleSubmitEmail,
    formState: { errors: emailErrors },
  } = useForm<EmailFormInputs>({
    defaultValues: {
      username: "",
    },
  });

  const {
    register: registerReset,
    handleSubmit: handleSubmitReset,
    watch,
    formState: { errors: resetErrors },
  } = useForm<ResetFormInputs>({
    defaultValues: {
      password: "",
      confirmPassword: "",
      code: "",
    },
  });

  // For password confirmation validation
  const password = watch("password");

  // Handler for submitting the email form
  const onSubmitEmail: SubmitHandler<EmailFormInputs> = async (data) => {
    setIsLoading(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Send verification code to the user's email
      const payload: ForgotPasswordPayload = { username: data.username };
      const response = await api.forgotPassword(payload);

      if (response.ok) {
        setFormSuccess(t("auth.forgotPassword.success.verificationCodeSent"));
        setUserEmail(data.username);
        setCurrentStep("passwordReset"); // Move to password reset step
      } else {
        setFormError(
          response.msg || t("auth.forgotPassword.errors.sendCodeFailed")
        );
      }
    } catch (err: any) {
      console.error("Forgot password error:", err);
      setFormError(
        err.message || t("auth.forgotPassword.errors.sendCodeFailed")
      );
    }
    setIsLoading(false);
  };

  // Handler for submitting the password reset form
  const onSubmitReset: SubmitHandler<ResetFormInputs> = async (data) => {
    if (data.password !== data.confirmPassword) {
      setFormError(t("auth.forgotPassword.errors.passwordsDoNotMatch"));
      return;
    }

    setIsLoading(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      const payload: ResetPasswordPayload = {
        username: userEmail,
        password: data.password,
        confirmPassword: data.confirmPassword,
        code: data.code,
      };
      const response = await api.resetPassword(payload);

      if (response.ok) {
        setFormSuccess(
          response.data ||
            t("auth.forgotPassword.success.passwordResetSuccessful")
        );
        setTimeout(() => {
          if (props.onOpenLogin) {
            props.onOpenLogin();
          } else {
            navigate("/login");
          }
        }, 2000);
      } else {
        setFormError(
          response.msg || t("auth.forgotPassword.errors.resetFailed")
        );
      }
    } catch (err: any) {
      console.error("Reset password error:", err);
      setFormError(err.message || t("auth.forgotPassword.errors.resetFailed"));
    }
    setIsLoading(false);
  };

  const handleBackToEmail = () => {
    setCurrentStep("emailEntry");
    setFormError(null);
    setFormSuccess(null);
  };

  return (
    <>
      {/* Content Area Wrapper - bottom for mobile, center for desktop */}
      <div className="relative z-10 flex flex-col flex-grow justify-end md:justify-center">
        {/* Form Card - rounded top for mobile, rounded all for desktop */}
        <motion.div
          className="w-full max-w-md mx-auto bg-[#1F2937] rounded-t-2xl md:rounded-2xl shadow-2xl lg:shadow-none p-4 space-y-6"
          initial={{ y: 0 }}
          animate={{ y: 0 }}
          transition={{ duration: 0 }}
        >
          {/* Header */}
          <div className="w-full flex items-center justify-center relative mb-0">
            {currentStep === "emailEntry" ? (
              <>
                {props.onOpenLogin ? (
                  <button
                    onClick={props.onOpenLogin}
                    className="absolute left-0 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 -ml-2 sm:-ml-3"
                  >
                    <ArrowLeft size={24} />
                  </button>
                ) : (
                  <Link
                    to="/login"
                    className="absolute left-0 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 -ml-2 sm:-ml-3"
                  >
                    <ArrowLeft size={24} />
                  </Link>
                )}
              </>
            ) : (
              <button
                onClick={handleBackToEmail}
                className="absolute left-0 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 -ml-2 sm:-ml-3"
              >
                <ArrowLeft size={24} />
              </button>
            )}
            <h2 className="text-lg sm:text-xl font-semibold text-white">
              {currentStep === "emailEntry"
                ? t("auth.forgotPassword.title")
                : t("auth.forgotPassword.resetTitle")}
            </h2>
          </div>

          {/* Step-specific content */}
          {currentStep === "emailEntry" ? (
            <>
              <div className="text-center mb-6">
                <p className="text-sm text-gray-400">
                  {t("auth.forgotPassword.subtitle")}
                </p>
              </div>

              <form
                className="w-full space-y-5"
                onSubmit={handleSubmitEmail(onSubmitEmail)}
              >
                <div>
                  <label
                    htmlFor="username"
                    className="block text-xs font-medium text-gray-400 mb-1"
                  >
                    {t("auth.forgotPassword.email")}
                  </label>
                  <Input
                    type="email"
                    id="username"
                    placeholder={t("auth.forgotPassword.emailPlaceholder")}
                    {...registerEmail("username", {
                      required: t("auth.forgotPassword.errors.emailRequired"),
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: t("auth.forgotPassword.errors.invalidEmail"),
                      },
                    })}
                    disabled={isLoading}
                  />
                  {emailErrors.username && (
                    <p className="text-red-500 text-xs mt-1">
                      {emailErrors.username.message}
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  fullWidth
                  className="py-3 text-base"
                  disabled={isLoading}
                >
                  {isLoading
                    ? t("auth.forgotPassword.submitting")
                    : t("auth.forgotPassword.submit")}
                </Button>

                {formError && (
                  <p className="text-red-500 text-xs mt-2 text-center">
                    {formError}
                  </p>
                )}

                {formSuccess && (
                  <p className="text-green-500 text-xs mt-2 text-center">
                    {formSuccess}
                  </p>
                )}
              </form>
            </>
          ) : (
            <>
              <div className="text-center mb-6">
                <p className="text-sm text-gray-400">
                  {t("auth.forgotPassword.resetSubtitle")}
                </p>
              </div>

              <form
                className="w-full space-y-5"
                onSubmit={handleSubmitReset(onSubmitReset)}
              >
                <div>
                  <label
                    htmlFor="code"
                    className="block text-xs font-medium text-gray-400 mb-1"
                  >
                    {t("auth.forgotPassword.verificationCode")}
                  </label>
                  <Input
                    type="text"
                    id="code"
                    placeholder={t(
                      "auth.forgotPassword.verificationCodePlaceholder"
                    )}
                    {...registerReset("code", {
                      required: t(
                        "auth.forgotPassword.errors.verificationCodeRequired"
                      ),
                    })}
                    disabled={isLoading}
                  />
                  {resetErrors.code && (
                    <p className="text-red-500 text-xs mt-1">
                      {resetErrors.code.message}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="password"
                    className="block text-xs font-medium text-gray-400 mb-1"
                  >
                    {t("auth.forgotPassword.password")}
                  </label>
                  <Input
                    type="password"
                    id="password"
                    placeholder={t("auth.forgotPassword.passwordPlaceholder")}
                    {...registerReset("password", {
                      required: t(
                        "auth.forgotPassword.errors.passwordRequired"
                      ),
                      minLength: {
                        value: 6,
                        message: t(
                          "auth.forgotPassword.errors.passwordMinLength"
                        ),
                      },
                    })}
                    isToggleablePassword
                    disabled={isLoading}
                  />
                  {resetErrors.password && (
                    <p className="text-red-500 text-xs mt-1">
                      {resetErrors.password.message}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="block text-xs font-medium text-gray-400 mb-1"
                  >
                    {t("auth.forgotPassword.confirmPassword")}
                  </label>
                  <Input
                    type="password"
                    id="confirmPassword"
                    placeholder={t(
                      "auth.forgotPassword.confirmPasswordPlaceholder"
                    )}
                    {...registerReset("confirmPassword", {
                      required: t(
                        "auth.forgotPassword.errors.confirmPasswordRequired"
                      ),
                      validate: (value) =>
                        value === password ||
                        t("auth.forgotPassword.errors.passwordsDoNotMatch"),
                    })}
                    isToggleablePassword
                    disabled={isLoading}
                  />
                  {resetErrors.confirmPassword && (
                    <p className="text-red-500 text-xs mt-1">
                      {resetErrors.confirmPassword.message}
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  fullWidth
                  className="py-3 text-base"
                  disabled={isLoading}
                >
                  {isLoading
                    ? t("auth.forgotPassword.submitting")
                    : t("auth.forgotPassword.submit")}
                </Button>

                {formError && (
                  <p className="text-red-500 text-xs mt-2 text-center">
                    {formError}
                  </p>
                )}

                {formSuccess && (
                  <p className="text-green-500 text-xs mt-2 text-center">
                    {formSuccess}
                  </p>
                )}
              </form>
            </>
          )}
        </motion.div>
      </div>
    </>
  );
}
