import { <PERSON>, useNavi<PERSON> } from "@remix-run/react";
import { Apple, ArrowLeft } from "lucide-react";
import { Button } from "~/components/ui/Button";
import { Input } from "~/components/ui/Input";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { createBase<PERSON>pi } from "~/utils/base-service";
import { LoginPayload } from "~/types/index";
import { useUser } from "~/context/auth-context";
import { useForm, SubmitHandler } from "react-hook-form";
import { useTranslation } from "react-i18next";

// Placeholder for actual social login SVGs if needed
const GoogleIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="currentColor"
    className="text-white"
  >
    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
  </svg>
);

interface LoginComponentProps {
  initialStep?: "emailEntry" | "passwordEntry";
  onOpenRegister?: () => void;
  onOpenForgotPassword?: () => void;
}

interface LoginFormInputs {
  username: string;
  password: string;
  smsCode?: string;
  remember: boolean;
}

export default function LoginComponent(props: LoginComponentProps) {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState<
    "emailEntry" | "passwordEntry"
  >(props.initialStep || "emailEntry");
  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const navigate = useNavigate();
  const api = createBaseApi();
  const { loginUser } = useUser();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<LoginFormInputs>({
    defaultValues: {
      username: "",
      password: "",
      smsCode: "",
      remember: false,
    },
  });

  // Watch the username field to use its value
  const username = watch("username");

  const handleContinueWithEmail = () => {
    if (username && username.trim() !== "") {
      setCurrentStep("passwordEntry");
      setFormError(null);
    } else {
      setFormError(t("auth.login.errors.enterUsername"));
    }
  };

  const handleBackToEmail = () => {
    setCurrentStep("emailEntry");
    setFormError(null);
  };

  const onSubmit: SubmitHandler<LoginFormInputs> = async (data) => {
    if (currentStep === "passwordEntry") {
      setIsLoading(true);
      setFormError(null);
      try {
        const payload: LoginPayload = {
          username: data.username,
          password: data.password,
          smsCode: data.smsCode,
        };
        const response = await api.login(payload);

        if (response.ok && response.data) {
          await loginUser(response.data);
          console.log("Login successful, navigating...");

          // Check for redirect URL and navigate accordingly
          const redirectUrl = localStorage.getItem("login_redirect_url");
          if (redirectUrl) {
            console.log("Redirecting to", redirectUrl);
            navigate(redirectUrl);
            localStorage.removeItem("login_redirect_url");
          } else {
            navigate("/");
          }
        } else {
          setFormError(response.msg || t("auth.login.errors.loginFailed"));
        }
      } catch (err: any) {
        console.error("Login error:", err);
        setFormError(err.message || t("auth.login.errors.unexpectedError"));
      }
      setIsLoading(false);
    }
  };

  // Full page version with background
  return (
    <>
      {/* Content Area Wrapper - bottom for mobile, center for desktop */}
      <div className="relative z-10 flex flex-col flex-grow justify-end md:justify-center">
        {/* Form Card - rounded top for mobile, rounded all for desktop */}
        <div className="w-full max-w-md mx-auto bg-[#1F2937] rounded-t-2xl md:rounded-2xl shadow-2xl lg:shadow-none p-4 space-y-6">
          {/* Step-specific Headers */}
          {currentStep === "emailEntry" ? (
            <>
              <div className="flex flex-col items-center text-center">
                <img
                  src="/logo.png"
                  alt="SnapDrama Logo"
                  className="h-32 sm:h-20 mb-2"
                />
              </div>
              <h2 className="text-xl sm:text-2xl font-semibold text-center text-white">
                {t("auth.login.title")}
              </h2>
            </>
          ) : (
            <div className="w-full flex items-center justify-center relative mb-0">
              {!props.initialStep && (
                <button
                  onClick={handleBackToEmail}
                  className="absolute left-0 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 -ml-2 sm:-ml-3"
                >
                  <ArrowLeft size={24} />
                </button>
              )}
              <h2 className="text-lg sm:text-xl font-semibold text-white">
                {t("auth.login.signIn")}
              </h2>
            </div>
          )}

          <form className="w-full space-y-5" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label
                htmlFor="username"
                className="block text-xs font-medium text-gray-400 mb-1"
              >
                {t("auth.login.usernameOrEmail")}
              </label>
              <Input
                type="text"
                id="username"
                placeholder={t("auth.login.usernameOrEmailPlaceholder")}
                {...register("username", {
                  required: t("auth.login.errors.usernameRequired"),
                })}
                disabled={isLoading}
              />
              {errors.username && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.username.message}
                </p>
              )}
            </div>

            <AnimatePresence initial={false}>
              {currentStep === "passwordEntry" && (
                <motion.div
                  key="passwordSection"
                  initial={{ opacity: 0, height: 0, marginTop: 0 }}
                  animate={{ opacity: 1, height: "auto", marginTop: "1.25rem" }}
                  exit={{ opacity: 0, height: 0, marginTop: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="overflow-hidden"
                >
                  <div className="space-y-5">
                    <div>
                      <label
                        htmlFor="password"
                        className="block text-xs font-medium text-gray-400 mb-1"
                      >
                        {t("auth.login.password")}
                      </label>
                      <Input
                        type="password"
                        id="password"
                        placeholder={t("auth.login.passwordPlaceholder")}
                        {...register("password", {
                          required: t("auth.login.errors.passwordRequired"),
                          minLength: {
                            value: 6,
                            message: t("auth.login.errors.passwordMinLength"),
                          },
                        })}
                        isToggleablePassword
                        disabled={isLoading}
                      />
                      {errors.password && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.password.message}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <label
                        htmlFor="remember"
                        className="flex items-center text-gray-400 cursor-pointer"
                      >
                        <input
                          id="remember"
                          type="checkbox"
                          className="h-4 w-4 text-red-600 border-gray-500 rounded bg-gray-700 focus:ring-red-500 mr-2"
                          {...register("remember")}
                        />
                        {t("auth.login.rememberMe")}
                      </label>
                      {props.onOpenForgotPassword ? (
                        <button
                          onClick={props.onOpenForgotPassword}
                          className="font-medium text-red-500 hover:text-red-400"
                        >
                          {t("auth.login.forgotPassword")}
                        </button>
                      ) : (
                        <Link
                          to="/forgot-password"
                          className="font-medium text-red-500 hover:text-red-400"
                        >
                          {t("auth.login.forgotPassword")}
                        </Link>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {currentStep === "emailEntry" ? (
              <>
                <Button
                  type="button"
                  variant="primary"
                  fullWidth
                  className="py-3 text-base"
                  onClick={handleContinueWithEmail}
                  disabled={isLoading}
                >
                  {t("auth.login.continueWithEmail")}
                </Button>
                {formError && (
                  <p className="text-red-500 text-xs mt-2 text-center">
                    {formError}
                  </p>
                )}
              </>
            ) : (
              <>
                <Button
                  type="submit"
                  variant="primary"
                  fullWidth
                  className="py-3 text-base"
                  disabled={isLoading}
                >
                  {isLoading
                    ? t("auth.login.signingIn")
                    : t("auth.login.signIn")}
                </Button>
                {formError && (
                  <p className="text-red-500 text-xs mt-2 text-center">
                    {formError}
                  </p>
                )}
              </>
            )}
          </form>

          <div className="flex items-center w-full py-1 sm:py-2">
            <hr className="flex-grow border-gray-600" />
            <span className="px-3 text-gray-400 text-xs">
              {t("auth.login.orContinueWith")}
            </span>
            <hr className="flex-grow border-gray-600" />
          </div>

          <div className="w-full space-y-3">
            <Button
              type="button"
              fullWidth
              className="bg-blue-600 hover:bg-blue-700 text-white py-3 text-sm"
              leftIcon={
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M9.198 21.5h4v-8.01h3.604l.396-3.98h-4V7.5a1 1 0 0 1 1-1h3v-4h-3a5 5 0 0 0-5 5v2.01h-2l-.396 3.98h2.396v8.01Z" />
                </svg>
              }
            >
              {t("auth.login.continueWithFacebook")}
            </Button>
            <Button
              type="button"
              fullWidth
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 text-sm"
              leftIcon={<GoogleIcon />}
            >
              {t("auth.login.continueWithGoogle")}
            </Button>
            <Button
              type="button"
              fullWidth
              variant="outline"
              className="border-gray-500 hover:bg-gray-700 text-white py-3 text-sm"
              leftIcon={<Apple size={20} />}
            >
              {t("auth.login.continueWithApple")}
            </Button>
          </div>

          <p className="text-center text-xs text-gray-400 pt-1 sm:pt-2">
            {t("auth.login.dontHaveAccount")}{" "}
            {props.onOpenRegister ? (
              <button
                onClick={props.onOpenRegister}
                className="font-medium text-red-500 hover:text-red-400"
              >
                {t("auth.login.signUp")}
              </button>
            ) : (
              <Link
                to="/register"
                state={{ animateRegister: true }}
                className="font-medium text-red-500 hover:text-red-400"
              >
                {t("auth.login.signUp")}
              </Link>
            )}
          </p>
        </div>
      </div>
    </>
  );
}
