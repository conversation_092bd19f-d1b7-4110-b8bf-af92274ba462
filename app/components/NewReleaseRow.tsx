import { Splide, SplideSlide } from "@splidejs/react-splide";
import "@splidejs/react-splide/css"; // Basic Splide CSS
// You might want to import a specific theme CSS if you prefer, e.g.:
// import '@splidejs/react-splide/css/skyblue';
// import '@splidejs/react-splide/css/sea-green';
// import '@splidejs/react-splide/css/core'; // Core CSS for advanced customization

import { useNavigate } from "@remix-run/react";
import type { ContentItem } from "~/types/index";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";

interface NewReleaseRowProps {
  title: string;
  items: ContentItem[];
}

const NewReleaseRow: React.FC<NewReleaseRowProps> = ({ title, items }) => {
  const { isDesktop } = useMediaQuery();
  const { navigateToVideo } = useVideoNavigation();

  if (!items || items.length === 0) {
    return null;
  }

  const splideOptions = {
    type: "loop", // 'loop' for infinite scroll, 'slide' for standard
    perPage: 7, // Number of slides to show at once (adjust based on design)
    perMove: 1, // Number of slides to move on arrow click
    gap: "1rem", // Gap between slides
    pagination: false, // Hide pagination dots
    arrows: isDesktop, // Show navigation arrows
    breakpoints: {
      1280: {
        // xl
        perPage: 6,
      },
      1024: {
        // lg
        perPage: 5,
      },
      768: {
        // md
        perPage: 4,
      },
      640: {
        // sm
        perPage: 3,
        gap: "0.75rem",
      },
      480: {
        // xs (custom, or adjust sm)
        perPage: 2,
        gap: "0.5rem",
      },
    },
    // Example: To ensure arrows are above fades if needed:
    // classes: {
    //   arrows: 'splide__arrows z-20',
    // },
  };

  return (
    <div className="mb-8">
      <h3 className="text-white text-xl font-semibold mb-4 px-4 md:px-0">
        {title}
      </h3>
      {/* Relative container for Splide and fade overlays */}
      {/* The px-4 md:px-0 here will make the fades appear within the content padding on mobile */}
      <div className="relative px-4 md:px-0">
        {/* Left fade overlay */}
        <div className="absolute hidden md:block inset-y-0 left-0 h-full w-16 sm:w-24 md:w-32 bg-gradient-to-r from-main-bg to-transparent z-10 pointer-events-none md:left-0"></div>

        <Splide options={splideOptions} aria-label={title}>
          {items.map((item, index) => (
            <SplideSlide key={index}>
              <div
                className="cursor-pointer group"
                onClick={() => navigateToVideo(item.id)}
              >
                <div className="aspect-[2/3] rounded-md overflow-hidden bg-gray-800 shadow-lg transition-transform duration-200 ease-in-out group-hover:scale-105 group-focus-within:ring-2 group-focus-within:ring-purple-500 group-focus-within:ring-offset-2 group-focus-within:ring-offset-black">
                  <img
                    src={item.thumbnailUrl}
                    alt={item.name}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
                <span className="text-white text-sm mt-2 truncate block text-center px-1 group-hover:text-gray-300">
                  {item.name}
                </span>
              </div>
            </SplideSlide>
          ))}
        </Splide>

        {/* Right fade overlay */}
        <div className="absolute inset-y-0 right-0 h-full w-16 sm:w-24 md:w-32 bg-gradient-to-l from-main-bg to-transparent z-10 pointer-events-none md:right-0"></div>
      </div>
    </div>
  );
};

export default NewReleaseRow;
