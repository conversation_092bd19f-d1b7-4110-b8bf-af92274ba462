import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm, SubmitHandler } from "react-hook-form";
import { Button } from "~/components/ui/Button";
import { Input } from "~/components/ui/Input";
import { useUser } from "~/context/auth-context";
import { baseApi } from "~/utils/base-service";
import {
  UserInfo,
  UserProfileDisplay,
  UpdatePersonalInfoPayload,
} from "~/types/index";

interface PersonalInfoInputs {
  fullName: string;
  email: string;
  phone: string;
  country: string;
}

interface PasswordInputs {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const AccountSettings = () => {
  const { t } = useTranslation();
  const { userInfo } = useUser();

  // Loading states for each form
  const [isPersonalInfoLoading, setIsPersonalInfoLoading] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);

  // Error and success states
  const [personalInfoError, setPersonalInfoError] = useState<string | null>(
    null
  );
  const [personalInfoSuccess, setPersonalInfoSuccess] = useState<string | null>(
    null
  );
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);

  // Default values for personal info
  const defaultPersonalInfo = {
    fullName: userInfo?.nickname || "",
    email: userInfo?.username || "",
    phone: "", // Phone is not in the UserInfo response, so we'll start with empty
    country: userInfo?.country || "",
  };

  // Personal Information Form
  const personalInfoForm = useForm<PersonalInfoInputs>({
    defaultValues: defaultPersonalInfo,
  });

  useEffect(() => {
    if (userInfo) {
      personalInfoForm.reset({
        fullName: userInfo.nickname || "",
        email: userInfo.username || "",
        phone: "",
        country: userInfo.country || "",
      });
    }
  }, [userInfo, personalInfoForm]);

  // Password Form
  const passwordForm = useForm<PasswordInputs>({
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // Watch for changes in personal info
  const watchedPersonalInfo = personalInfoForm.watch();
  const hasPersonalInfoChanged =
    JSON.stringify(watchedPersonalInfo) !== JSON.stringify(defaultPersonalInfo);

  // Watch for changes in password
  const watchedPassword = passwordForm.watch();
  const hasPasswordChanged =
    watchedPassword.currentPassword ||
    watchedPassword.newPassword ||
    watchedPassword.confirmPassword;

  // Personal Info Submit Handler
  const onPersonalInfoSubmit: SubmitHandler<PersonalInfoInputs> = async (
    data
  ) => {
    setIsPersonalInfoLoading(true);
    setPersonalInfoError(null);
    setPersonalInfoSuccess(null);

    try {
      const payload = transformPersonalInfoPayload(data);
      const response = await baseApi.updatePersonalInfo(payload);

      if (response.ok) {
        setPersonalInfoSuccess(
          t("settings.account.success.personalInfoUpdated")
        );
      } else {
        setPersonalInfoError(
          response.msg || t("settings.account.errors.updateFailed")
        );
      }
    } catch (err: any) {
      console.error("Personal info update error:", err);
      setPersonalInfoError(
        err.message || t("settings.account.errors.updateFailed")
      );
    }
    setIsPersonalInfoLoading(false);
  };

  // Password Submit Handler
  const onPasswordSubmit: SubmitHandler<PasswordInputs> = async (data) => {
    setIsPasswordLoading(true);
    setPasswordError(null);
    setPasswordSuccess(null);

    // Validate password fields
    if (!data.currentPassword) {
      setPasswordError(t("settings.account.errors.currentPasswordRequired"));
      setIsPasswordLoading(false);
      return;
    }
    if (!data.newPassword) {
      setPasswordError(t("settings.account.errors.newPasswordRequired"));
      setIsPasswordLoading(false);
      return;
    }
    if (data.newPassword !== data.confirmPassword) {
      setPasswordError(t("settings.account.errors.passwordsDoNotMatch"));
      setIsPasswordLoading(false);
      return;
    }

    try {
      const { fullName, email, phone, country } = personalInfoForm.getValues();
      const payload = {
        nickname: fullName,
        country,
        phone,
        email,
        password: data.currentPassword,
        newPassword: data.newPassword,
      };
      const response = await baseApi.updatePersonalInfo(payload);

      if (response.ok) {
        setPasswordSuccess(t("settings.account.success.passwordUpdated"));
        // Reset password form after successful update
        passwordForm.reset();
      } else {
        setPasswordError(
          response.msg || t("settings.account.errors.updateFailed")
        );
      }
    } catch (err: any) {
      console.error("Password update error:", err);
      setPasswordError(
        err.message || t("settings.account.errors.updateFailed")
      );
    }
    setIsPasswordLoading(false);
  };

  return (
    <div className="flex-1 p-6 lg:p-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="mb-2">
            <h1 className="text-3xl font-bold text-white">
              {t("settings.account.title")}
            </h1>
          </div>
          <p className="text-gray-400 text-lg">
            Manage your personal information and security settings
          </p>
        </div>

        <div className="space-y-8">
          {/* Personal Information Section */}
          <form
            onSubmit={personalInfoForm.handleSubmit(onPersonalInfoSubmit)}
            className="bg-neutral-900/50 backdrop-blur-sm rounded-2xl p-6 lg:p-8 border border-neutral-800"
          >
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-white mb-2">
                {t("settings.account.personalInfo.title")}
              </h2>
              <p className="text-gray-400 text-sm">
                {t("settings.account.personalInfo.subtitle")}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="space-y-2">
                <label
                  htmlFor="fullName"
                  className="block text-sm font-medium text-gray-300 mb-3"
                >
                  {t("settings.account.personalInfo.fullName")}
                </label>
                <div className="relative">
                  <Input
                    type="text"
                    id="fullName"
                    {...personalInfoForm.register("fullName", {
                      required: t("settings.account.errors.fullNameRequired"),
                    })}
                    disabled={isPersonalInfoLoading}
                    className="bg-neutral-800/50 border-neutral-700 text-white focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 pl-4 pr-4 py-3 rounded-xl"
                  />
                </div>
                {personalInfoForm.formState.errors.fullName && (
                  <p className="text-red-400 text-sm mt-2">
                    {personalInfoForm.formState.errors.fullName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-300 mb-3"
                >
                  {t("settings.account.personalInfo.email")}
                </label>
                <div className="relative">
                  <Input
                    type="email"
                    id="email"
                    {...personalInfoForm.register("email", {
                      required: t("settings.account.errors.emailRequired"),
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: t("settings.account.errors.invalidEmail"),
                      },
                    })}
                    disabled={isPersonalInfoLoading}
                    className="bg-neutral-800/50 border-neutral-700 text-white focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 pl-4 pr-4 py-3 rounded-xl"
                  />
                </div>
                {personalInfoForm.formState.errors.email && (
                  <p className="text-red-400 text-sm mt-2">
                    {personalInfoForm.formState.errors.email.message}
                  </p>
                )}
              </div>

              <div className="md:col-span-2 space-y-2">
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-300 mb-3"
                >
                  {t("settings.account.personalInfo.phone")}
                </label>
                <div className="relative">
                  <Input
                    type="tel"
                    id="phone"
                    {...personalInfoForm.register("phone", {
                      //   required: t("settings.account.errors.phoneRequired"),
                    })}
                    disabled={isPersonalInfoLoading}
                    className="bg-neutral-800/50 border-neutral-700 text-white focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 pl-4 pr-4 py-3 rounded-xl"
                  />
                </div>
                {personalInfoForm.formState.errors.phone && (
                  <p className="text-red-400 text-sm mt-2">
                    {personalInfoForm.formState.errors.phone.message}
                  </p>
                )}
              </div>
            </div>

            {/* Personal Info Messages */}
            {personalInfoError && (
              <div className="p-4 bg-red-900/20 border border-red-500/20 rounded-xl backdrop-blur-sm mb-4">
                <p className="text-red-400 text-sm">{personalInfoError}</p>
              </div>
            )}

            {personalInfoSuccess && (
              <div className="p-4 bg-green-900/20 border border-green-500/20 rounded-xl backdrop-blur-sm mb-4">
                <p className="text-green-400 text-sm">{personalInfoSuccess}</p>
              </div>
            )}

            {/* Personal Info Save Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                variant="primary"
                disabled={isPersonalInfoLoading || !hasPersonalInfoChanged}
                className="px-8 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 flex items-center space-x-2 min-w-[140px] justify-center"
              >
                {isPersonalInfoLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>{t("settings.account.saving")}</span>
                  </>
                ) : (
                  <span>Save Personal Info</span>
                )}
              </Button>
            </div>
          </form>

          {/* Password Section */}
          <form
            onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
            className="bg-neutral-900/50 backdrop-blur-sm rounded-2xl p-6 lg:p-8 border border-neutral-800"
          >
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-white mb-2">
                {t("settings.account.password.title")}
              </h2>
              <p className="text-gray-400 text-sm">
                {t("settings.account.password.subtitle")}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="md:col-span-2 space-y-2">
                <label
                  htmlFor="currentPassword"
                  className="block text-sm font-medium text-gray-300 mb-3"
                >
                  {t("settings.account.password.currentPassword")}
                </label>
                <div className="relative">
                  <Input
                    type="password"
                    id="currentPassword"
                    placeholder={t(
                      "settings.account.password.currentPasswordPlaceholder"
                    )}
                    {...passwordForm.register("currentPassword")}
                    isToggleablePassword
                    disabled={isPasswordLoading}
                    className="bg-neutral-800/50 border-neutral-700 text-white focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 pl-4 pr-12 py-3 rounded-xl"
                  />
                </div>
                {passwordForm.formState.errors.currentPassword && (
                  <p className="text-red-400 text-sm mt-2">
                    {passwordForm.formState.errors.currentPassword.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="newPassword"
                  className="block text-sm font-medium text-gray-300 mb-3"
                >
                  {t("settings.account.password.newPassword")}
                </label>
                <div className="relative">
                  <Input
                    type="password"
                    id="newPassword"
                    placeholder={t(
                      "settings.account.password.newPasswordPlaceholder"
                    )}
                    {...passwordForm.register("newPassword", {
                      minLength: {
                        value: 6,
                        message: t("settings.account.errors.passwordMinLength"),
                      },
                    })}
                    isToggleablePassword
                    disabled={isPasswordLoading}
                    className="bg-neutral-800/50 border-neutral-700 text-white focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 pl-4 pr-12 py-3 rounded-xl"
                  />
                </div>
                {passwordForm.formState.errors.newPassword && (
                  <p className="text-red-400 text-sm mt-2">
                    {passwordForm.formState.errors.newPassword.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="confirmPassword"
                  className="block text-sm font-medium text-gray-300 mb-3"
                >
                  {t("settings.account.password.confirmPassword")}
                </label>
                <div className="relative">
                  <Input
                    type="password"
                    id="confirmPassword"
                    placeholder={t(
                      "settings.account.password.confirmPasswordPlaceholder"
                    )}
                    {...passwordForm.register("confirmPassword", {
                      validate: (value: string) =>
                        !passwordForm.watch("newPassword") ||
                        value === passwordForm.watch("newPassword") ||
                        t("settings.account.errors.passwordsDoNotMatch"),
                    })}
                    isToggleablePassword
                    disabled={isPasswordLoading}
                    className="bg-neutral-800/50 border-neutral-700 text-white focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 pl-4 pr-12 py-3 rounded-xl"
                  />
                </div>
                {passwordForm.formState.errors.confirmPassword && (
                  <p className="text-red-400 text-sm mt-2">
                    {passwordForm.formState.errors.confirmPassword.message}
                  </p>
                )}
              </div>
            </div>

            {/* Password Messages */}
            {passwordError && (
              <div className="p-4 bg-red-900/20 border border-red-500/20 rounded-xl backdrop-blur-sm mb-4">
                <p className="text-red-400 text-sm">{passwordError}</p>
              </div>
            )}

            {passwordSuccess && (
              <div className="p-4 bg-green-900/20 border border-green-500/20 rounded-xl backdrop-blur-sm mb-4">
                <p className="text-green-400 text-sm">{passwordSuccess}</p>
              </div>
            )}

            {/* Password Save Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                variant="primary"
                disabled={isPasswordLoading || !hasPasswordChanged}
                className="px-8 py-3 bg-green-500 hover:bg-green-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 flex items-center space-x-2 min-w-[140px] justify-center"
              >
                {isPasswordLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>{t("settings.account.saving")}</span>
                  </>
                ) : (
                  <span>Change Password</span>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AccountSettings;

/**
 * Helper function to format UserInfo for display
 */
export const formatUserInfoForDisplay = (
  userInfo: UserInfo
): UserProfileDisplay => {
  return {
    displayName: userInfo.nickname,
    email: userInfo.username,
    joinDate: userInfo.crtTime,
    membershipStatus: userInfo.vip ? "vip" : "free",
    membershipExpiry: userInfo.vipTime,
    goldBalance: userInfo.gold,
    isVip: userInfo.vip,
  };
};

/**
 * Helper function to transform form data to API payload for personal info update
 */
export const transformPersonalInfoPayload = (formData: {
  fullName: string;
  email: string;
  phone: string;
  country: string;
}): UpdatePersonalInfoPayload => {
  return {
    nickname: formData.fullName,
    country: formData.country,
    phone: formData.phone,
    email: formData.email,
    password: "",
    newPassword: "",
  };
};
