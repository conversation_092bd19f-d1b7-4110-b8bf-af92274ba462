import React, { useState, useRef, useCallback, useEffect } from "react";
import ShortVideo from "./short-video";
import { useNavigate, useSearchParams } from "@remix-run/react";
import { useSwipeNavigation } from "~/hooks/useSwipeNavigation";
import EpisodeDrawer from "./video/EpisodeDrawer";
import { VideoData } from "~/types/videos";
import { mockVideosEpisodes } from "~/mock/videos";
import { EpisodeItem } from "~/types/index";

interface VideoFeedProps {
  videoData: VideoData;
  episodes?: EpisodeItem[];
}

export default function VideoFeed({
  videoData,
  episodes = [],
}: VideoFeedProps) {
  const [searchParams] = useSearchParams();
  const activeEpisodeId = searchParams.get("episodeId");
  // Convert single video to array if needed
  const [videos, setVideos] = useState<VideoData[]>(
    Array.isArray(videoData) ? videoData : [videoData]
  );
  const navigate = useNavigate();
  const containerRef = useRef<HTMLDivElement>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Use the custom swipe navigation hook
  const {
    currentIndex,
    setCurrentIndex,
    isDragging,
    dragOffset,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  } = useSwipeNavigation({
    itemCount: episodes.length,
  });

  // Log initial state and changes
  // console.log("VideoFeed render, drawerOpen:", drawerOpen);

  const handleNextVideo = useCallback(() => {
    if (episodes.length === 0) return;
    navigate(`?episodeId=${episodes[currentIndex + 1].id}`, {
      replace: true,
    });
    setCurrentIndex((prevIndex) => {
      const nextIndex = Math.min(prevIndex + 1, episodes.length - 1);
      if (nextIndex > prevIndex) {
        const currentEpisode = videos[prevIndex].currentEpisodeNumber || 1;
        const totalEpisodes = videos.length; // Use array length for total

        const updatedVideos = [...videos];
        if (currentEpisode < totalEpisodes) {
          updatedVideos[nextIndex] = {
            ...updatedVideos[nextIndex],
            currentEpisodeNumber: currentEpisode + 1,
          };
          setVideos(updatedVideos);
        }
      }

      return nextIndex;
    });
  }, [episodes]);

  useEffect(() => {
    if (currentIndex >= 0 && episodes.length > 0) {
      const episodeId = episodes[currentIndex].id;
      navigate(`?episodeId=${episodes[currentIndex].id}`, {
        replace: true,
      });
    }
  }, [currentIndex, episodes]);

  useEffect(() => {
    if (activeEpisodeId && episodes.length > 0) {
      const episodeIndex = episodes.findIndex(
        (episode) => episode.id == activeEpisodeId
      );

      setCurrentIndex(episodeIndex);
    }
  }, [activeEpisodeId, episodes]);

  // Get episodes data from props or generate from videos if needed

  // --- Placeholder Callbacks ---
  const handleClose = () => {
    navigate("/videos/1");
    console.log("Close action triggered from ShortVideo");
  };

  const handleLike = (liked: boolean, currentLikes: number) => {
    console.log(
      `Video ${videos[currentIndex].id} Liked: ${liked}, Count: ${currentLikes}`
    );
  };

  const handleSave = (saved: boolean) => {
    console.log(`Video ${videos[currentIndex].id} Saved: ${saved}`);
  };

  const handleShare = () => {
    console.log(`Share video ${videos[currentIndex].id}`);
  };

  const handleComment = () => {
    console.log(`Comment on video ${videos[currentIndex].id}`);
  };

  const handleProfileClick = () => {
    console.log(`View profile for ${videos[currentIndex].username}`);
  };

  if (!videos || videos.length === 0) {
    return <div>Loading videos...</div>; // Or some placeholder
  }

  const selectedEpisode = episodes.find(
    (episode) => episode.id === activeEpisodeId
  );
  return (
    <div
      ref={containerRef}
      className="fixed top-16 left-0 right-0 bottom-0 bg-black overflow-hidden" // Adjusted to account for AppHeader height (64px = 16rem)
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{ touchAction: "pan-y" }} // Helps optimize touch scrolling behavior
    >
      {" "}
      {/* Video container - Handles transform and transition */}
      <div
        className={`h-full w-full ${
          !isDragging ? "transition-transform duration-300 ease-in-out" : ""
        } z-30`}
        style={{
          transform: `translateY(calc(-${
            currentIndex * 100
          }% + ${dragOffset}px))`,
        }}
      >
        {episodes.map((episode, index) => (
          <div
            key={`${episode.id}-${
              index === currentIndex ? "active" : "inactive"
            }`}
            className="w-full h-full flex-shrink-0"
          >
            {/* <div className="text-white">{`${index} ${currentIndex}`}</div> */}
            <ShortVideo
              key={`video-${videoData.id}-${
                index === currentIndex ? "current" : index
              }`}
              muted={index !== currentIndex}
              isActive={index === currentIndex} // Pass isActive prop to indicate current video
              // Pass individual props from the video object
              videoUrl={episode?.playUrl}
              subtitleUrl={episode?.subtitleUrl}
              posterUrl={videoData.posterUrl}
              profileImageUrl={videoData.profileImageUrl}
              username={videoData.username}
              title={videoData.title}
              description={videoData.description}
              tags={videoData.tags}
              initialLikes={videoData.initialLikes}
              initialComments={videoData.initialComments}
              initialShares={videoData.initialShares}
              initialLiked={videoData.initialLiked}
              initialSaved={videoData.initialSaved}
              locked={videoData.locked}
              // Pass handlers down
              onClose={handleClose}
              // Pass video-specific handlers if needed, or use index from map
              onLike={(liked, likes) => handleLike(liked, likes)} // Simplified for now
              onSave={handleSave}
              onShare={handleShare}
              onComment={handleComment}
              onProfileClick={handleProfileClick}
              // Only pass onVideoEnd to the currently active video
              onVideoEnd={index === currentIndex ? handleNextVideo : undefined}
            />
          </div>
        ))}
      </div>
      {/* Episode Drawer Component */}
      <EpisodeDrawer
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        seriesTitle={videoData.seriesTitle || "Unknown Series"}
        seasonNumber={videoData.seasonNumber || 1}
        totalEpisodes={videos.length}
        currentEpisode={videoData.currentEpisodeNumber || 1}
        episodes={episodes}
        onEpisodeSelect={() => setDrawerOpen(false)}
        videoData={videoData}
      />
    </div>
  );
}
