import { Trash2, Play, X, Upload } from "lucide-react";
import { useState, useRef } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { Button } from "~/components/ui/Button";
import { Input } from "~/components/ui/Input";

const mockVideos = [
  {
    id: 1,
    title: "Money Heist",
    poster: "/images/black-widow.jpg",
  },
  {
    id: 2,
    title: "Money Heist",
    poster: "/images/iron-man-3.jpg",
  },
  {
    id: 3,
    title: "Money Heist",
    poster: "/images/harry-meghan.jpg",
  },
];

interface UploadFormData {
  title: string;
  description: string;
  videoFile: File | null;
  coverImage: File | null;
}

const VideoSettings = () => {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [formData, setFormData] = useState<UploadFormData>({
    title: "",
    description: "",
    videoFile: null,
    coverImage: null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const videoFileInputRef = useRef<HTMLInputElement>(null);
  const coverImageInputRef = useRef<HTMLInputElement>(null);

  const handleVideoFileSelect = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData((prev) => ({ ...prev, videoFile: file }));
    }
  };

  const handleCoverImageSelect = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData((prev) => ({ ...prev, coverImage: file }));
    }
  };

  const handleSubmit = async () => {
    if (!formData.title.trim() || !formData.videoFile) {
      return;
    }

    setIsSubmitting(true);
    try {
      // TODO: Implement actual upload API call
      console.log("Uploading video:", formData);

      // Simulate upload delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Reset form and close dialog
      setFormData({
        title: "",
        description: "",
        videoFile: null,
        coverImage: null,
      });
      setUploadDialogOpen(false);
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getVideoPreviewUrl = () => {
    if (formData.videoFile) {
      return URL.createObjectURL(formData.videoFile);
    }
    return null;
  };

  const getCoverImagePreviewUrl = () => {
    if (formData.coverImage) {
      return URL.createObjectURL(formData.coverImage);
    }
    return null;
  };

  return (
    <div className="flex-1 p-6 lg:p-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="mb-2">
            <h1 className="text-3xl font-bold text-white">My Drama</h1>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-6">
          {/* Upload Card */}
          <div
            className="w-full h-64 bg-neutral-800 rounded-xl flex flex-col items-center justify-center p-4 text-center border-2 border-dashed border-neutral-600 cursor-pointer hover:border-neutral-500 transition-colors"
            onClick={() => setUploadDialogOpen(true)}
          >
            <div className="flex flex-col items-center mb-4">
              <div className="bg-red-500 rounded-full p-3 mb-2">
                <Upload size={32} className="text-white" />
              </div>
              <span className="text-white text-sm font-medium">
                Drag and drop video files to upload
              </span>
            </div>
            <button className="bg-white text-black rounded-md px-4 py-2 text-sm font-semibold mb-2">
              Select files
            </button>
            <span className="text-xs text-neutral-400">Max 1GB</span>
          </div>

          {/* Video Cards */}
          {mockVideos.map((video) => (
            <div
              key={video.id}
              className="relative w-full h-64 bg-neutral-900 rounded-xl overflow-hidden group flex-shrink-0"
            >
              <img
                src={video.poster}
                alt={video.title}
                className="w-full h-48 object-cover"
              />
              {/* Play Button Overlay */}
              <button className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <span className="bg-black/60 rounded-full p-3">
                  <Play size={32} className="text-white" />
                </span>
              </button>
              {/* Delete Button */}
              <button className="absolute top-2 right-2 bg-red-500 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Trash2 size={16} className="text-white" />
              </button>
              {/* Title */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent px-2 py-2">
                <span className="text-white text-sm font-medium block truncate">
                  {video.title}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Upload Video Dialog */}
      <Dialog.Root open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/70 z-[60] data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut" />
          <Dialog.Content className="fixed inset-0 z-[61] overflow-y-auto outline-none">
            <div className="flex items-center justify-center min-h-screen p-4">
              <div className="w-full max-w-4xl bg-[#1F2937] rounded-2xl shadow-2xl max-h-[90vh] overflow-y-auto text-white relative">
                {/* Close button */}
                <Dialog.Close className="absolute top-4 right-4 text-gray-400 hover:text-white z-10">
                  <X size={24} />
                </Dialog.Close>

                {/* Dialog Header */}
                <div className="px-6 py-4 border-b border-gray-600">
                  <Dialog.Title className="text-xl font-semibold text-white">
                    Upload Video
                  </Dialog.Title>
                </div>

                {/* Dialog Content */}
                <div className="flex flex-col lg:flex-row">
                  {/* Left Side - Video Preview */}
                  <div className="lg:w-1/2 p-6">
                    <div className="aspect-video bg-black rounded-lg overflow-hidden relative">
                      {formData.videoFile ? (
                        <video
                          src={getVideoPreviewUrl() || undefined}
                          className="w-full h-full object-cover"
                          controls
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <div className="text-center">
                            <div className="bg-red-500 rounded-full p-4 mx-auto mb-4 w-16 h-16 flex items-center justify-center">
                              <Play size={32} className="text-white" />
                            </div>
                            <p className="text-gray-400">No video selected</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Video File Input */}
                    <div className="mt-4">
                      <input
                        ref={videoFileInputRef}
                        type="file"
                        accept="video/*"
                        onChange={handleVideoFileSelect}
                        className="hidden"
                      />
                      <Button
                        variant="secondary"
                        onClick={() => videoFileInputRef.current?.click()}
                        className="w-full"
                      >
                        {formData.videoFile ? "Change Video" : "Select Video"}
                      </Button>
                    </div>
                  </div>

                  {/* Right Side - Form */}
                  <div className="lg:w-1/2 p-6 border-l border-gray-600">
                    <div className="space-y-6">
                      {/* Title Field */}
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Title (required)
                        </label>
                        <Input
                          type="text"
                          placeholder="Enter Video Title"
                          value={formData.title}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              title: e.target.value,
                            }))
                          }
                          className="w-full"
                        />
                      </div>

                      {/* Description Field */}
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Description
                        </label>
                        <textarea
                          placeholder="Tell viewer about your video"
                          value={formData.description}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              description: e.target.value,
                            }))
                          }
                          className="w-full px-4 py-3 bg-[#2D3748] border border-gray-600 focus-visible:outline-none rounded-lg focus:ring-brand-red focus:border-brand-red placeholder-gray-500 text-white resize-none"
                          rows={4}
                          maxLength={200}
                        />
                        <div className="text-right text-xs text-gray-400 mt-1">
                          {formData.description.length}/200
                        </div>
                      </div>

                      {/* Cover Photo Section */}
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Cover Photo
                        </label>
                        <p className="text-xs text-gray-400 mb-3">
                          Set a thumbnail that stands out and draws viewers'
                          attention.
                        </p>

                        <div className="flex items-center space-x-4">
                          <div className="w-24 h-16 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                            {formData.coverImage ? (
                              <img
                                src={getCoverImagePreviewUrl() || undefined}
                                alt="Cover preview"
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Upload size={16} className="text-gray-400" />
                              </div>
                            )}
                          </div>

                          <div className="flex-1">
                            <input
                              ref={coverImageInputRef}
                              type="file"
                              accept="image/*"
                              onChange={handleCoverImageSelect}
                              className="hidden"
                            />
                            <button
                              onClick={() =>
                                coverImageInputRef.current?.click()
                              }
                              className="text-sm text-blue-400 hover:text-blue-300"
                            >
                              Drag and drop cover image to upload
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Submit Button */}
                      <div className="pt-4">
                        <Button
                          variant="primary"
                          onClick={handleSubmit}
                          disabled={
                            !formData.title.trim() ||
                            !formData.videoFile ||
                            isSubmitting
                          }
                          className="w-full py-3"
                        >
                          {isSubmitting ? "Uploading..." : "Submit"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </div>
  );
};

export default VideoSettings;
