import React from "react";
import * as Dialog from "@radix-ui/react-dialog";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useMediaQuery } from "~/hooks/useMediaQuery";

export interface DropdownOption {
  id: string;
  label: string;
  icon?: React.ReactNode;
  action?: () => void;
}

interface ResponsiveDropdownProps {
  options: DropdownOption[];
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  trigger: React.ReactNode;
  title?: string;
  align?: "start" | "center" | "end";
  sideOffset?: number;
  triggerClassName?: string;
  contentClassName?: string;
  itemClassName?: string;
}

const ResponsiveDropdown: React.FC<ResponsiveDropdownProps> = ({
  options,
  open,
  onOpenChange,
  trigger,
  title,
  align = "end",
  sideOffset = 5,
  triggerClassName,
  contentClassName,
  itemClassName,
}) => {
  const { isDesktop } = useMediaQuery();

  const handleSelect = (option: DropdownOption) => {
    if (option.action) {
      option.action();
    }
    onOpenChange?.(false);
  };

  const dialogContentStyle: React.CSSProperties = {
    top: `calc(env(safe-area-inset-top, 0px) + 4rem)`,
    maxHeight: `calc(100vh - (env(safe-area-inset-top, 0px) + 4rem) - env(safe-area-inset-bottom, 0px))`,
  };

  if (isDesktop) {
    return (
      <DropdownMenu.Root open={open} onOpenChange={onOpenChange}>
        <DropdownMenu.Trigger asChild>{trigger}</DropdownMenu.Trigger>
        <DropdownMenu.Portal>
          <DropdownMenu.Content
            className={`bg-neutral-900 border border-neutral-700 rounded-lg shadow-xl mt-1 py-1 w-56 z-[52] animate-fadeIn ${
              contentClassName || ""
            }`}
            sideOffset={sideOffset}
            align={align}
          >
            {options.map((option) => (
              <DropdownMenu.Item
                key={option.id}
                className={`flex items-center px-3 py-2 text-sm rounded-md mx-1 my-0.5 cursor-pointer text-white hover:bg-neutral-800 focus:outline-none focus:bg-neutral-800 ${
                  itemClassName || ""
                }`}
                onClick={() => handleSelect(option)}
              >
                {option.icon && (
                  <span className="mr-3 text-gray-400">{option.icon}</span>
                )}
                <span>{option.label}</span>
              </DropdownMenu.Item>
            ))}
          </DropdownMenu.Content>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>
    );
  }

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Trigger asChild>{trigger}</Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 z-[51] data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut" />
        <Dialog.Content
          style={dialogContentStyle}
          className="fixed left-0 right-0 bg-neutral-900 rounded-b-xl overflow-y-auto z-[52] outline-none shadow-lg data-[state=open]:animate-slideDownFromTop data-[state=closed]:animate-slideUpToTop"
        >
          {title && (
            <div className="px-5 py-4 border-b border-neutral-800">
              <Dialog.Title className="text-xl font-normal text-white">
                {title}
              </Dialog.Title>
            </div>
          )}
          <div className="pt-2 px-2 pb-safe">
            {options.map((option) => (
              <button
                key={option.id}
                className={`w-full flex items-center px-3 py-3 text-base rounded-lg my-1 text-white hover:bg-neutral-800 focus:outline-none ${
                  itemClassName || ""
                }`}
                onClick={() => handleSelect(option)}
              >
                {option.icon && (
                  <span className="text-xl mr-3 text-gray-400">
                    {option.icon}
                  </span>
                )}
                <span className="font-normal">{option.label}</span>
              </button>
            ))}
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default ResponsiveDropdown;
