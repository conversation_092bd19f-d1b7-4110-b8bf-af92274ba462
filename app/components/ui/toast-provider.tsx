import React, {
  createContext,
  useState,
  useCallback,
  useContext,
  ReactNode,
} from "react";
import * as Toast from "@radix-ui/react-toast";
import { useTranslation } from "react-i18next";

export type ToastType = "success" | "error" | "info";

interface ToastContextType {
  showToast: (message: string, type?: ToastType, duration?: number) => void;
  showSuccessToast: (message: string, duration?: number) => void;
  showErrorToast: (message: string, duration?: number) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastMessage {
  id: string;
  message: string;
  duration: number;
  type: ToastType;
}

export const ToastProviderComponent = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);
  const { t } = useTranslation();

  const showToast = useCallback(
    (messageKey: string, type: ToastType = "info", duration: number = 3000) => {
      const id = new Date().toISOString() + Math.random();
      setToasts((currentToasts) => [
        ...currentToasts,
        { id, message: messageKey, duration, type },
      ]);
    },
    []
  );

  const showSuccessToast = useCallback(
    (messageKey: string, duration?: number) => {
      showToast(messageKey, "success", duration);
    },
    [showToast]
  );

  const showErrorToast = useCallback(
    (messageKey: string, duration?: number) => {
      showToast(messageKey, "error", duration);
    },
    [showToast]
  );

  const removeToast = useCallback((id: string) => {
    setToasts((currentToasts) =>
      currentToasts.filter((toast) => toast.id !== id)
    );
  }, []);

  const getToastClassName = (type: ToastType) => {
    let baseClasses =
      "rounded-md shadow-lg p-4 text-white data-[state=open]:animate-slideInFromLeft data-[state=closed]:animate-slideOutToLeft data-[swipe=move]:translate-x-var-radix-toast-swipe-move-x data-[swipe=cancel]:translate-x-0 data-[swipe=end]:animate-swipeOut";
    switch (type) {
      case "success":
        return `${baseClasses} bg-green-600`;
      case "error":
        return `${baseClasses} bg-red-600`;
      case "info":
      default:
        return `${baseClasses} bg-gray-800`;
    }
  };

  return (
    <ToastContext.Provider
      value={{ showToast, showSuccessToast, showErrorToast }}
    >
      <Toast.Provider swipeDirection="right">
        {children}
        {toasts.map(({ id, message, duration, type }) => (
          <Toast.Root
            key={id}
            open={true}
            onOpenChange={(isOpen) => {
              if (!isOpen) {
                removeToast(id);
              }
            }}
            className={getToastClassName(type)}
            duration={duration}
          >
            <Toast.Title className="font-medium">{t(message)}</Toast.Title>
          </Toast.Root>
        ))}
        <Toast.Viewport className="fixed bottom-0 right-0 flex flex-col p-6 gap-3 w-[390px] max-w-[100vw] m-0 list-none z-[**********] outline-none" />
      </Toast.Provider>
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error("useToast must be used within a ToastProviderComponent");
  }
  return context;
};
