import { Link } from "@remix-run/react";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import type { ContentItem } from "~/types/index";

interface MovieCardProps {
  item: ContentItem;
  /** Optional custom className for the container */
  className?: string;
  /** Whether to show accessibility features like focus states and keyboard navigation */
  withAccessibility?: boolean;
  /** Custom text size for the title */
  titleSize?: "xs" | "sm" | "base" | "lg";
  /** Custom hover color for the title */
  titleHoverColor?: string;
  /** Custom aria-label prefix for accessibility */
  ariaLabelPrefix?: string;
}

const MovieCard = ({
  item,
  className = "",
  withAccessibility = false,
  titleSize = "xs",
  titleHoverColor = "text-gray-300",
  ariaLabelPrefix = "Play",
}: MovieCardProps) => {
  const { isDesktop } = useMediaQuery();

  // Determine the URL based on device type (same logic as useVideoNavigation)
  const getVideoUrl = () => {
    // Mobile devices go to play page, desktop to detail page
    if (!isDesktop) {
      return `/videos/play/${item.id}`;
    } else {
      return `/videos/${item.id}`;
    }
  };

  const titleSizeClasses = {
    xs: "text-xs",
    sm: "text-sm",
    base: "text-base",
    lg: "text-lg",
  };

  const baseContainerClasses = "cursor-pointer group";
  const accessibilityClasses = withAccessibility ? "focus:outline-none" : "";
  const containerClasses = `${baseContainerClasses} ${accessibilityClasses} ${className}`;

  const baseImageClasses =
    "aspect-[2/3] rounded-md overflow-hidden bg-gray-800 shadow-lg transition-transform duration-200 ease-in-out group-hover:scale-105";
  const accessibilityImageClasses = withAccessibility
    ? "group-focus:scale-105 group-focus:ring-2 group-focus:ring-red-500 group-focus:ring-offset-2 group-focus:ring-offset-black"
    : "";
  const imageClasses = `${baseImageClasses} ${accessibilityImageClasses}`;

  const baseTitleClasses = `text-white ${titleSizeClasses[titleSize]} mt-2 truncate block text-center px-1 transition-colors`;
  const titleHoverClasses = withAccessibility
    ? "group-hover:text-red-400"
    : `group-hover:${titleHoverColor}`;
  const titleClasses = `${baseTitleClasses} ${titleHoverClasses}`;

  const linkProps = withAccessibility
    ? {
        "aria-label": `${ariaLabelPrefix} ${item.name}`,
      }
    : {};

  return (
    <Link to={getVideoUrl()} className={containerClasses} {...linkProps}>
      <div className={imageClasses}>
        <img
          src={item.thumbnailUrl}
          alt={item.name}
          className="w-full h-full object-cover"
          loading="lazy"
        />
      </div>
      <span className={titleClasses}>{item.name}</span>
    </Link>
  );
};

export default MovieCard;
