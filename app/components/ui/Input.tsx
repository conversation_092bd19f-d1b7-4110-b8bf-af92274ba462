import React, { useState } from "react";
import { cn } from "~/utils/cn";
import { Eye, EyeOff } from "lucide-react";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onLeftIconClick?: () => void;
  onRightIconClick?: () => void;
  isToggleablePassword?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      leftIcon,
      rightIcon: customRightIcon,
      onLeftIconClick,
      onRightIconClick: customOnRightIconClick,
      isToggleablePassword,
      ...props
    },
    ref
  ) => {
    const [isPasswordActuallyVisible, setIsPasswordActuallyVisible] =
      useState(false);

    const effectivelyIsPassword = type === "password" && isToggleablePassword;

    const currentType = effectivelyIsPassword
      ? isPasswordActuallyVisible
        ? "text"
        : "password"
      : type;

    const handleToggleVisibility = () => {
      if (effectivelyIsPassword) {
        setIsPasswordActuallyVisible(!isPasswordActuallyVisible);
      }
    };

    let iconToRender = customRightIcon;
    let iconClickHandler = customOnRightIconClick;

    if (effectivelyIsPassword) {
      iconToRender = isPasswordActuallyVisible ? (
        <EyeOff size={20} className="text-gray-400" />
      ) : (
        <Eye size={20} className="text-gray-400" />
      );
      iconClickHandler = handleToggleVisibility;
    }

    return (
      <div className="relative w-full">
        {leftIcon && (
          <div
            className="absolute inset-y-0 left-0 pl-3 flex items-center cursor-pointer"
            onClick={onLeftIconClick}
          >
            {leftIcon}
          </div>
        )}
        <input
          type={currentType}
          className={cn(
            "w-full px-4 py-3 bg-[#2D3748] border border-gray-600 focus-visible:outline-none rounded-lg focus:ring-brand-red focus:border-brand-red placeholder-gray-500 text-white",
            className,
            leftIcon ? "pl-10" : "",
            iconToRender ? "pr-10" : ""
          )}
          ref={ref}
          {...props}
        />
        {iconToRender && (
          <div
            className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
            onClick={iconClickHandler}
          >
            {iconToRender}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input };
