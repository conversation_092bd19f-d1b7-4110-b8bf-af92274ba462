import type { ContentItem } from "~/types/index";
import { Star, FolderKanban } from "lucide-react";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";

interface ContentItemCardProps {
  item: ContentItem;
  /** Optional custom className for the container */
  className?: string;
  /** Custom image dimensions */
  imageSize?: {
    width: string;
    height: string;
  };
  /** Whether to show season information */
  showSeason?: boolean;
  /** Custom season number (defaults to 1) */
  seasonNumber?: number;
  /** Custom click handler (overrides default navigation) */
  onClick?: (item: ContentItem) => void;
}

const ContentItemCard = ({
  item,
  className = "",
  imageSize = {
    width: "w-24 sm:w-28",
    height: "h-36 sm:h-42",
  },
  showSeason = true,
  seasonNumber = 1,
  onClick,
}: ContentItemCardProps) => {
  const { navigateToVideo } = useVideoNavigation();

  const handleClick = () => {
    if (onClick) {
      onClick(item);
    } else {
      navigateToVideo(item.id);
    }
  };

  return (
    <div
      className={`flex p-3 rounded-lg cursor-pointer group hover:bg-gray-800/70 transition-colors duration-200 ${className}`}
      onClick={handleClick}
    >
      <div className={`${imageSize.width} ${imageSize.height} rounded-md overflow-hidden bg-gray-700 shrink-0 shadow-md`}>
        <img
          src={item.thumbnailUrl}
          alt={item.name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
        />
      </div>
      <div className="ml-4 flex flex-col justify-center text-white flex-grow min-w-0">
        <h4 className="text-base sm:text-lg font-semibold truncate mb-1 group-hover:text-gray-200">
          {item.name}
        </h4>
        {showSeason && (
          <div className="text-xs sm:text-sm text-gray-400 mb-1 space-x-1">
            <span>
              Season <span className="text-red-500 font-medium">{seasonNumber}</span>{" "}
              {/* Placeholder Season */}
            </span>
            {item.episodes && (
              <>
                <span>•</span>
                <span>
                  Episodes{" "}
                  <span className="text-red-500 font-medium">
                    {item.episodes}
                  </span>
                </span>
              </>
            )}
          </div>
        )}
        {item.genre && (
          <div className="text-xs sm:text-sm text-gray-400 mb-1 flex items-center">
            <FolderKanban
              size={14}
              className="mr-1.5 text-gray-500 shrink-0"
            />
            <span className="truncate">
              {Array.isArray(item.genre) ? item.genre.join(", ") : item.genre}
            </span>
          </div>
        )}
        {item.rating && (
          <div className="text-xs sm:text-sm text-yellow-400 flex items-center">
            <Star size={14} className="mr-1 fill-yellow-400 shrink-0" />
            <span>{item.rating.toFixed(1)}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentItemCard;
