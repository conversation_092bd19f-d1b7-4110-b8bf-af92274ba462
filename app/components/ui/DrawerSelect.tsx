import React from "react";
import { ChevronDown } from "lucide-react";
import * as Dialog from "@radix-ui/react-dialog";

export interface SelectOption {
  id: string;
  country?: string;
  label: string;
  icon?: React.ReactNode;
}

interface DrawerSelectProps {
  options: SelectOption[];
  selectedId: string;
  title?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onChange: (option: SelectOption) => void;
  slideFrom?: "top" | "bottom";
}

const DrawerSelect: React.FC<DrawerSelectProps> = ({
  options,
  selectedId,
  title,
  open,
  onOpenChange,
  onChange,
  slideFrom = "bottom",
}) => {
  const selectedOptionForTrigger =
    options.find((option) => option.id === selectedId) || options[0];

  const handleSelect = (option: SelectOption) => {
    onChange(option);
    onOpenChange?.(false); // Close dialog on selection
  };

  const isTop = slideFrom === "top";

  const dialogContentClasses = `
    fixed ${isTop ? "top-0" : "bottom-0"} left-0 right-0 
    bg-main-bg ${isTop ? "rounded-b-xl" : "rounded-t-xl"} 
    max-h-[85vh] overflow-y-auto z-50 
    ${
      isTop
        ? "data-[state=open]:animate-slideDownFromTop data-[state=closed]:animate-slideUpToTop"
        : "data-[state=open]:animate-slideUp data-[state=closed]:animate-slideDownToBottom"
    } 
    ${isTop ? "pt-6" : "pb-6"} outline-none
  `;

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Trigger asChild>
        <button
          className="flex items-center space-x-1 text-white bg-transparent p-1"
          aria-expanded={open}
          aria-haspopup="dialog"
        >
          {selectedOptionForTrigger.icon && (
            <span className="text-xl">{selectedOptionForTrigger.icon}</span>
          )}
          <ChevronDown
            size={16}
            className={`transition-transform duration-200 ${
              open ? "rotate-180" : ""
            }`}
          />
        </button>
      </Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 z-50 data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut" />
        <Dialog.Content
          className={dialogContentClasses.replace(/\s+/g, " ").trim()}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Drawer header */}
          {title && (
            <div
              className={`px-5 py-4 ${
                isTop ? "border-t" : "border-b"
              } border-neutral-800`}
            >
              <Dialog.Title className="text-xl font-normal text-white">
                {title}
              </Dialog.Title>
            </div>
          )}

          {/* Options list */}
          <div className={`pt-2 px-2 ${isTop ? "pb-safe" : ""}`}>
            {options.map((option) => {
              const isSelected = option.id === selectedId;
              return (
                <button
                  key={option.id}
                  className={`w-full flex items-center px-3 py-3 text-base rounded-lg my-1 ${
                    isSelected
                      ? "bg-neutral-700 text-brand-red"
                      : "text-white hover:bg-neutral-800 "
                  } focus:outline-none`}
                  onClick={() => handleSelect(option)}
                >
                  {option.icon && (
                    <span className="text-xl mr-3">{option.icon}</span>
                  )}
                  <span
                    className={`font-normal ${
                      isSelected ? "text-brand-red" : "text-white"
                    }`}
                  >
                    {option.label}
                  </span>
                </button>
              );
            })}
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default DrawerSelect;
