import { useState, useRef, useEffect } from "react";
import { Globe } from "lucide-react";
import { languageOptions } from "~/utils/languageOptions";
import { useLanguageStore } from "~/stores/languageStore";
import { useTranslation } from "react-i18next";
import { changeLanguage } from "~/utils/languageUtils";
import { useLocation, useNavigate } from "@remix-run/react";

interface VideoLanguageSelectorProps {
  onLanguageChange?: (languageId: string) => void;
  className?: string; // Optional className for additional styling
}

export default function VideoLanguageSelector({
  onLanguageChange,
  className = "",
}: VideoLanguageSelectorProps) {
  const { videoLanguage, setVideoLanguage } = useLanguageStore();
  const [isLanguagePopoverOpen, setIsLanguagePopoverOpen] = useState(false);
  const languagePopoverRef = useRef<HTMLDivElement>(null);
  const { i18n } = useTranslation();

  // Check if we're in a browser environment
  const isBrowser = typeof window !== "undefined";
  const location = useLocation();
  const navigate = useNavigate();
  // Effect to sync store changes with component callback
  useEffect(() => {
    if (onLanguageChange) {
      onLanguageChange(videoLanguage);
    }
  }, [videoLanguage, onLanguageChange]);

  // Effect to sync videoLanguage with i18n.language changes
  useEffect(() => {
    const currentI18nLang = i18n.language;
    if (!currentI18nLang || !isBrowser) return;

    const baseI18nLang = currentI18nLang.split("-")[0];
    let targetLangToSet: string | undefined = undefined;

    // Check if the full i18n language (e.g., "en-US") or its base (e.g., "en")
    // is one of the available options for this selector.
    if (languageOptions.some((opt) => opt.id === currentI18nLang)) {
      targetLangToSet = currentI18nLang;
    } else if (languageOptions.some((opt) => opt.id === baseI18nLang)) {
      targetLangToSet = baseI18nLang;
    }

    // If a suitable language is found in options and it's different from the current videoLanguage, update the store.
    if (targetLangToSet && targetLangToSet !== videoLanguage) {
      setVideoLanguage(targetLangToSet);
    }
  }, [
    i18n.language,
    videoLanguage,
    setVideoLanguage,
    isBrowser,
    languageOptions,
  ]);

  // Effect to handle clicking outside the language popover - only runs in browser
  useEffect(() => {
    if (!isBrowser) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        languagePopoverRef.current &&
        !languagePopoverRef.current.contains(event.target as Node)
      ) {
        setIsLanguagePopoverOpen(false);
      }
    };

    if (isLanguagePopoverOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isLanguagePopoverOpen, isBrowser]);

  // Function to handle language change
  const handleLanguageChange = (languageId: string) => {
    setVideoLanguage(languageId);

    changeLanguage({
      newLanguage: languageId,
      i18n,
      onSuccess: () => setIsLanguagePopoverOpen(false),
      location,
      navigate,
    });
  };

  // Don't render anything on server
  if (!isBrowser) {
    return null;
  }

  return (
    <div
      className={`relative hidden sm:block md:ml-3 ${className}`}
      ref={languagePopoverRef}
    >
      <button
        className="p-1 relative"
        onClick={(e) => {
          e.stopPropagation(); // Prevent video click-through
          setIsLanguagePopoverOpen(!isLanguagePopoverOpen);
        }}
        title="Select language"
      >
        <Globe size={20} />
        <span className="absolute -bottom-2 -right-1 text-[10px] font-bold">
          {videoLanguage.toUpperCase()}
        </span>
      </button>

      {/* Language Selection Popover */}
      {isLanguagePopoverOpen && (
        <div
          className="absolute bottom-full mb-2 bg-black/80 backdrop-blur-sm rounded-md p-1 w-36 max-h-48 overflow-y-auto shadow-lg border border-white/10 z-50"
          style={{ left: "50%", transform: "translateX(-50%)" }}
        >
          {languageOptions.map((option) => (
            <button
              key={option.id}
              className={`w-full text-left px-2 py-1.5 text-sm rounded hover:bg-white/10 flex items-center gap-2
                ${
                  videoLanguage === option.id ? "bg-white/20 font-medium" : ""
                }`}
              onClick={(e) => {
                e.stopPropagation();
                handleLanguageChange(option.id);
              }}
            >
              <span>{option.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
