/**
 * Enhanced AddToWatchlistButton Component
 *
 * A highly customizable button component for adding/removing videos from watchlist
 * with enhanced visual feedback and multiple styling variants.
 *
 * Features:
 * - Smart icon switching (Plus/Check or Bookmark/BookmarkCheck)
 * - Multiple visual variants (default, minimal, floating)
 * - Enhanced color schemes for different states
 * - Smooth transitions and hover effects
 * - Accessibility support with proper ARIA labels
 * - Automatic authentication handling
 *
 * @example
 * // Basic usage
 * <AddToWatchlistButton videoId="123" />
 *
 * // With bookmark icons and floating style
 * <AddToWatchlistButton
 *   videoId="123"
 *   variant="floating"
 *   useBookmarkIcon={true}
 * />
 *
 * // Minimal icon-only version
 * <AddToWatchlistButton
 *   videoId="123"
 *   variant="minimal"
 *   iconOnly={true}
 *   size="sm"
 * />
 */

import { Plus, Check, Bookmark, BookmarkCheck } from "lucide-react";
import { useTranslation } from "react-i18next";
import Button from "~/components/ui/Button";
import { useUser } from "~/context/auth-context";
import { useCollect, useUpdateRel } from "~/hooks/usePraiseCollect";

interface AddToWatchlistButtonProps {
  /** The video ID to add/remove from watchlist */
  videoId: string;
  /** Optional custom className for the button */
  className?: string;
  /** Button size variant */
  size?: "sm" | "md" | "lg";
  /** Whether to show only an icon (for compact layouts) */
  iconOnly?: boolean;
  /** Custom icon size */
  iconSize?: number;
  /** Callback function called after successful watchlist toggle */
  onToggle?: (isInWatchlist: boolean) => void;
  /** Whether to show loading state */
  disabled?: boolean;
  /** Visual style variant */
  variant?: "default" | "minimal" | "floating";
  /** Whether to use bookmark icons instead of plus/check */
  useBookmarkIcon?: boolean;
}

export default function AddToWatchlistButton({
  videoId,
  className = "",
  size = "md",
  iconOnly = false,
  iconSize = 20,
  onToggle,
  disabled = false,
  variant = "default",
  useBookmarkIcon = false,
}: AddToWatchlistButtonProps) {
  const { t } = useTranslation();
  const { userInfo, isLoggedIn } = useUser();
  const updateRelMutation = useUpdateRel();
  const { data: collectItems = [] } = useCollect();

  // Check if video is in watchlist
  const isVideoInWatchlist = (id: string) =>
    collectItems.some((item) => item.id === id);

  const isInWatchlist = isVideoInWatchlist(videoId);

  const handleWatchlistToggle = async () => {
    if (!isLoggedIn || !userInfo || !videoId) {
      console.log("User must be logged in to add to watchlist");
      return;
    }

    try {
      const currentlyInWatchlist = isInWatchlist;
      const newWatchlistState = !currentlyInWatchlist;

      await updateRelMutation.mutateAsync({
        userId: userInfo.id,
        postId: videoId,
        type: "collect",
        value: newWatchlistState,
      });

      console.log(
        `${
          newWatchlistState
            ? t("video.addedToWatchlist")
            : t("video.removedFromWatchlist")
        }`
      );

      // Call the optional callback
      onToggle?.(newWatchlistState);
    } catch (error) {
      console.error("Error updating watchlist:", error);
    }
  };

  // Early return if user is not logged in
  if (!isLoggedIn || !userInfo || !videoId) {
    return <></>;
  }

  // Determine icon based on state and preference
  const getIcon = () => {
    if (useBookmarkIcon) {
      return isInWatchlist ? (
        <BookmarkCheck
          size={iconSize}
          className="transition-all duration-200"
        />
      ) : (
        <Bookmark size={iconSize} className="transition-all duration-200" />
      );
    } else {
      return isInWatchlist ? (
        <Check size={iconSize} className="transition-all duration-200" />
      ) : (
        <Plus size={iconSize} className="transition-all duration-200" />
      );
    }
  };

  // Determine button variant and styling based on watchlist status and variant
  const getButtonVariant = () => {
    if (variant === "minimal") {
      return "ghost";
    }
    if (variant === "floating") {
      return isInWatchlist ? "secondary" : "outline";
    }
    // Default variant
    return isInWatchlist ? "secondary" : "outline";
  };

  // Enhanced styling based on variant and state
  const getEnhancedClassName = () => {
    const baseClasses = className;

    if (variant === "minimal") {
      return `${baseClasses} ${
        isInWatchlist
          ? "text-green-400 hover:text-green-300 bg-green-400/10 hover:bg-green-400/20"
          : "text-gray-400 hover:text-white bg-gray-400/10 hover:bg-gray-400/20"
      } border-none transition-all duration-200`;
    }

    if (variant === "floating") {
      return `${baseClasses} ${
        isInWatchlist
          ? "bg-green-600 hover:bg-green-700 text-white border-green-600 shadow-lg shadow-green-600/25"
          : "bg-gray-800/90 hover:bg-gray-700 text-white border-gray-600 shadow-lg backdrop-blur-sm"
      } transition-all duration-200 transform hover:scale-105`;
    }

    // Default enhanced styling
    return `${baseClasses} ${
      isInWatchlist
        ? "bg-green-600 hover:bg-green-700 text-white border-green-600 shadow-md"
        : "bg-transparent hover:bg-gray-800 text-white border-gray-600 hover:border-gray-500"
    } transition-all duration-200`;
  };

  // Determine button text with enhanced messaging
  const buttonText = isInWatchlist
    ? t("video.addedToWatchlist", "Added to Watchlist")
    : t("featured.addWatchlist", "Add to Watchlist");

  // Enhanced aria-label for better accessibility
  const ariaLabel = isInWatchlist
    ? t("watchlist.removeFromWatchlist", "Remove from watchlist")
    : t("watchlist.addToWatchlist", "Add to watchlist");

  return (
    <Button
      variant={getButtonVariant()}
      size={size}
      onClick={handleWatchlistToggle}
      disabled={disabled || !isLoggedIn}
      leftIcon={getIcon()}
      className={getEnhancedClassName()}
      aria-label={ariaLabel}
    >
      {!iconOnly && (
        <span
          className={`transition-all duration-200 ${
            isInWatchlist ? "font-medium" : ""
          }`}
        >
          {buttonText}
        </span>
      )}
    </Button>
  );
}
