import React, { useCallback } from "react";
import { ChevronDown } from "lucide-react";
import * as Dialog from "@radix-ui/react-dialog";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useMediaQuery } from "~/hooks/useMediaQuery"; // Import the hook
import { type SelectOption } from "./DrawerSelect";
import { uiEvents } from "~/utils/analytics"; // Update import to use specific event module

interface ResponsiveLanguageSelectProps {
  options: SelectOption[];
  selectedId: string;
  title?: string; // Title for the mobile drawer
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onChange: (option: SelectOption) => void;
  triggerClassName?: string;
}

const ResponsiveLanguageSelect: React.FC<ResponsiveLanguageSelectProps> = ({
  options,
  selectedId,
  title,
  open,
  onOpenChange,
  onChange,
  triggerClassName = "flex items-center space-x-1 text-white bg-transparent p-1",
}) => {
  const { isDesktop } = useMediaQuery();
  const selectedOptionForTrigger =
    options.find((option) => option.id === selectedId) || options[0];

  // Track when language selector is opened
  const handleOpenChange = useCallback(
    (isOpen: boolean) => {
      if (isOpen && typeof window !== "undefined") {
        // Use the centralized uiEvents.languageSelectorOpened function
        uiEvents.languageSelectorOpened(
          isDesktop,
          selectedId,
          selectedOptionForTrigger?.label || selectedId
        );
      }

      // Pass the state up to parent component
      onOpenChange?.(isOpen);
    },
    [isDesktop, onOpenChange, selectedId, selectedOptionForTrigger]
  );

  const handleSelect = (option: SelectOption) => {
    onChange(option);
    handleOpenChange(false);
  };

  const dialogContentStyle: React.CSSProperties = {
    top: `calc(env(safe-area-inset-top, 0px) + 4rem)`,
    maxHeight: `calc(100vh - (env(safe-area-inset-top, 0px) + 4rem) - env(safe-area-inset-bottom, 0px))`,
  };

  // Common Trigger Button - Corrected aria-haspopup type
  const TriggerButton = React.forwardRef<
    HTMLButtonElement,
    {
      "aria-expanded"?: boolean;
      "aria-haspopup"?: "menu" | "dialog" | "listbox" | "tree" | "grid";
    }
  >((props, ref) => (
    <button
      ref={ref}
      className={triggerClassName}
      {...props} // Spread props here
    >
      {selectedOptionForTrigger.icon && (
        <span className="text-xl">{selectedOptionForTrigger.icon}</span>
      )}
      <ChevronDown
        size={16}
        className={`transition-transform duration-200 ${
          open ? "rotate-180" : ""
        }`}
      />
    </button>
  ));
  TriggerButton.displayName = "TriggerButton";

  if (isDesktop) {
    return (
      <DropdownMenu.Root open={open} onOpenChange={handleOpenChange}>
        <DropdownMenu.Trigger asChild>
          {/* Explicitly pass props expected by asChild and the custom TriggerButton */}
          <TriggerButton aria-expanded={open || false} aria-haspopup="menu" />
        </DropdownMenu.Trigger>
        <DropdownMenu.Portal>
          <DropdownMenu.Content
            className="bg-main-bg border border-neutral-700 rounded-lg shadow-xl mt-1 py-1 w-56 z-[52] animate-fadeIn"
            sideOffset={5}
            align="end"
          >
            {options.map((option) => {
              const isSelected = option.id === selectedId;
              return (
                <DropdownMenu.Item
                  key={option.id}
                  className={`flex items-center px-3 py-2 text-sm rounded-md mx-1 my-0.5 cursor-pointer ${
                    isSelected
                      ? "bg-neutral-700 text-brand-red"
                      : "text-white hover:bg-neutral-800"
                  } focus:outline-none focus:bg-neutral-800`}
                  onClick={() => handleSelect(option)}
                  aria-selected={isSelected}
                >
                  {option.icon && (
                    <span className="text-xl mr-3">{option.icon}</span>
                  )}
                  <span
                    className={isSelected ? "text-brand-red" : "text-white"}
                  >
                    {option.label}
                  </span>
                </DropdownMenu.Item>
              );
            })}
          </DropdownMenu.Content>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>
    );
  }

  // MOBILE: Use Radix Dialog (Drawer - existing logic)
  return (
    <Dialog.Root open={open} onOpenChange={handleOpenChange}>
      <Dialog.Trigger asChild>
        {/* Explicitly pass props expected by asChild and the custom TriggerButton */}
        <TriggerButton aria-expanded={open || false} aria-haspopup="dialog" />
      </Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 z-[51] data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut" />
        <Dialog.Content
          style={dialogContentStyle}
          className="fixed left-0 right-0 bg-main-bg rounded-b-xl overflow-y-auto z-[52] outline-none shadow-lg data-[state=open]:animate-slideDownFromTop data-[state=closed]:animate-slideUpToTop"
        >
          {title && (
            <div className="px-5 py-4 border-b border-neutral-800">
              <Dialog.Title className="text-xl font-normal text-white">
                {title}
              </Dialog.Title>
            </div>
          )}
          <div className="pt-2 px-2 pb-safe">
            {options.map((option) => {
              const isSelected = option.id === selectedId;
              return (
                <button
                  key={option.id}
                  className={`w-full flex items-center px-3 py-3 text-base rounded-lg my-1 ${
                    isSelected
                      ? "bg-neutral-700 text-brand-red"
                      : "text-white hover:bg-neutral-800"
                  } focus:outline-none`}
                  onClick={() => handleSelect(option)}
                >
                  {option.icon && (
                    <span className="text-xl mr-3">{option.icon}</span>
                  )}
                  <span
                    className={`font-normal ${
                      isSelected ? "text-brand-red" : "text-white"
                    }`}
                  >
                    {option.label}
                  </span>
                </button>
              );
            })}
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default ResponsiveLanguageSelect;
