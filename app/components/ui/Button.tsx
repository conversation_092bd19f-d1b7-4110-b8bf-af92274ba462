import React from "react";
import { cn } from "~/utils/cn";
import { cva, type VariantProps } from "class-variance-authority";

const buttonVariants = cva(
  "rounded-lg px-6 py-3 font-semibold transition-colors duration-200 flex items-center justify-center",
  {
    variants: {
      variant: {
        primary: "bg-brand-red text-white hover:bg-red-700",
        outline:
          "border border-white text-white bg-transparent hover:bg-white/10",
        secondary:
          "bg-black/50 backdrop-blur-sm border border-neutral-600 text-white hover:bg-neutral-700",
        ghost: "bg-transparent text-white hover:bg-white/10",
      },
      fullWidth: {
        true: "w-full",
      },
      size: {
        sm: "px-4 py-2 text-sm",
        md: "px-6 py-3 text-base",
        lg: "px-8 py-4 text-lg",
      },
    },
    defaultVariants: {
      variant: "primary",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  // isLoading?: boolean; // Add if you need loading states
  // asChild?: boolean; // Add if you need to render as a child component
}

/**
 * Button component with custom styling
 */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      fullWidth,
      leftIcon,
      rightIcon,
      children,
      // isLoading,
      // disabled,
      ...props
    },
    ref
  ) => {
    return (
      <button
        className={cn(buttonVariants({ variant, fullWidth, size, className }))}
        ref={ref}
        // disabled={isLoading || disabled}
        {...props}
      >
        {/* {isLoading && <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />} */}
        {leftIcon && <span className="mr-2">{leftIcon}</span>}
        {children}
        {rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
