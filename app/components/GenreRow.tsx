import { ChevronRight } from "lucide-react";
import type { ContentItem, TopicItem } from "~/types/index";
import Button from "~/components/ui/Button";
import MovieCard from "~/components/ui/MovieCard";
import { useGenreFilter } from "~/hooks/useGenreFilter";
import { useTranslation } from "react-i18next";
import { Splide, SplideSlide } from "@splidejs/react-splide";
import "@splidejs/react-splide/css/core"; // Core Splide CSS for base styling
// You might also need other Splide themes/CSS depending on desired appearance e.g. @splidejs/react-splide/css/skyblue

interface GenreRowProps {
  title: string;
  topics?: TopicItem[]; // Optional topics from API
  initialItems?: ContentItem[]; // Optional initial items to display
}

export default function GenreRow({
  title,
  topics = [],
  initialItems = [],
}: GenreRowProps) {
  const { t } = useTranslation();
  const { filterResult, filterByTopic } = useGenreFilter();

  // Use topics from props or from the hook
  const availableTopics = topics.length > 0 ? topics : filterResult.topics;

  // Use initial items or filtered results
  const displayItems = filterResult.selectedTopicId
    ? filterResult.results
    : initialItems;

  if (!availableTopics || availableTopics.length === 0) {
    return null;
  }

  const allTabsIncludingAllOption = [null, ...availableTopics];

  const handleTopicTabClick = (topic: TopicItem | null) => {
    filterByTopic(topic?.id || null);
  };

  const getTopicTabDisplayName = (topic: TopicItem | null) => {
    if (topic === null) {
      return t("genres.all");
    }
    return t(topic.title, { defaultValue: topic.title });
  };

  const itemsToDisplay = displayItems.slice(0, 12);

  const seeMoreText = t("seeMore", "See More");
  const playText = t("play", "Play");
  const noContentText = t(
    "genres.noContent",
    "No content available for this selection."
  );

  const splideOptions = {
    autoWidth: true,
    arrows: true, // Show arrows, will be styled by global CSS
    pagination: false,
    gap: "0.75rem", // Tailwind class `space-x-3` equivalent
    drag: "free" as const, // Explicitly type as const for Splide's string literal types
    snap: false, // No snap to align with free drag
    flickPower: 200, // Adjust flick power if needed
    // Ensure there's enough padding if arrows are positioned outside and overlap content area
    // padding: { left: 30, right: 30 }, // Example: if arrows are large and overlay content area edges
  };

  return (
    <div className="mb-12 px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white text-2xl font-bold">{title}</h3>
        <Button
          variant="ghost"
          className="text-sm text-gray-300 hover:text-white transition-colors flex items-center p-0 h-auto hover:bg-transparent"
        >
          {seeMoreText}
          <ChevronRight size={18} className="ml-1 -mt-px" />
        </Button>
      </div>

      {/* Genre Tabs Navigation with Splide and Fade Effect */}
      <div className="mb-6 splide-fade-wrapper">
        {" "}
        {/* Added splide-fade-wrapper class */}
        <Splide options={splideOptions} aria-label={`${title} topics carousel`}>
          {allTabsIncludingAllOption.map((topic) => {
            const isSelected =
              filterResult.selectedTopicId === (topic?.id || null);
            const isAllTab = topic === null;

            const baseTabClasses =
              "px-3 sm:px-4 py-2 rounded-full text-sm font-medium transition-all duration-150 ease-in-out whitespace-nowrap flex-shrink-0";
            let tabSpecificClasses = "";

            if (isSelected) {
              if (isAllTab) {
                tabSpecificClasses = "bg-brand-red text-white";
              } else {
                tabSpecificClasses = "bg-slate-600 text-white";
              }
            } else {
              tabSpecificClasses =
                "bg-zinc-800 text-gray-300 hover:bg-zinc-700 hover:text-white";
            }

            return (
              <SplideSlide
                key={topic?.id ?? "all-topics-tab"}
                className="first:ml-8 last:mr-8"
              >
                <button
                  type="button"
                  className={`${baseTabClasses} ${tabSpecificClasses}`}
                  onClick={() => handleTopicTabClick(topic)}
                  aria-selected={isSelected}
                  style={{ display: "block" }} // Ensure button takes up slide space if needed
                >
                  {getTopicTabDisplayName(topic)}
                </button>
              </SplideSlide>
            );
          })}
        </Splide>
      </div>

      {/* Content Display Area - Grid for video items */}
      <div className="grid grid-cols-3 lg:grid-cols-6 gap-x-4 gap-y-6">
        {filterResult.isLoading ? (
          <div className="col-span-full text-center py-10">
            <p className="text-gray-400 text-lg">
              {t("loading", "Loading...")}
            </p>
          </div>
        ) : itemsToDisplay.length > 0 ? (
          itemsToDisplay.map((item) => (
            <MovieCard
              key={item.id}
              item={item}
              withAccessibility={true}
              titleSize="base"
              ariaLabelPrefix={playText}
            />
          ))
        ) : (
          <div className="col-span-full text-center py-10">
            <p className="text-gray-400 text-lg">{noContentText}</p>
          </div>
        )}
      </div>
    </div>
  );
}
