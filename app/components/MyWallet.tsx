import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { createBaseApi } from "~/utils/base-service";
import type { WalletEvent, WalletEventsPayload } from "~/types/index";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function MyWallet() {
  const { t } = useTranslation();
  const [transactions, setTransactions] = useState<WalletEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const pageSize = 10;

  useEffect(() => {
    fetchWalletEvents();
  }, [currentPage]);

  const fetchWalletEvents = async () => {
    try {
      setIsLoading(true);
      const api = createBaseApi();
      const payload: WalletEventsPayload = {
        pageNumber: currentPage,
        pageSize: pageSize,
      };

      console.log("Fetching wallet events with payload:", payload);
      const response = await api.getWalletEvents(payload);
      console.log("Wallet events response:", response);

      if (response.ok && response.data) {
        setTransactions(response.data.list);
        setTotalTransactions(response.data.total);
        setTotalPages(Math.ceil(response.data.total / pageSize));
      } else {
        console.error("Failed to fetch wallet events:", response.msg);
      }
    } catch (error) {
      console.error("Error fetching wallet events:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (money: number) => {
    // For now, all transactions are completed since we don't have status field
    // We can differentiate by money amount (negative = expense, positive = income)
    return "bg-green-600 text-green-100";
  };

  const getTransactionType = (tag: string, remark: string) => {
    if (tag === "购买会员" || remark.includes("会员")) {
      return "VIP Membership";
    } else if (remark.includes("充值") || remark.includes("Top Up")) {
      return "Top Up";
    }
    return tag; // fallback to original tag
  };

  const formatAmount = (money: number) => {
    const absAmount = Math.abs(money);
    return `¥${absAmount.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const renderPaginationButtons = () => {
    const buttons = [];
    const maxVisiblePages = 5;

    // Previous button
    buttons.push(
      <button
        key="prev"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="px-3 py-2 rounded-lg bg-gray-700 text-white disabled:bg-gray-800 disabled:text-gray-500 hover:bg-gray-600 transition-colors"
      >
        <ChevronLeft size={16} />
      </button>
    );

    // Page numbers
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-3 py-2 rounded-lg transition-colors ${
            i === currentPage
              ? "bg-red-600 text-white"
              : "bg-gray-700 text-white hover:bg-gray-600"
          }`}
        >
          {i}
        </button>
      );
    }

    // Show ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        buttons.push(
          <span key="ellipsis" className="px-3 py-2 text-gray-400">
            ...
          </span>
        );
      }
      buttons.push(
        <button
          key={totalPages}
          onClick={() => handlePageChange(totalPages)}
          className="px-3 py-2 rounded-lg bg-gray-700 text-white hover:bg-gray-600 transition-colors"
        >
          {totalPages}
        </button>
      );
    }

    // Next button
    buttons.push(
      <button
        key="next"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="px-3 py-2 rounded-lg bg-gray-700 text-white disabled:bg-gray-800 disabled:text-gray-500 hover:bg-gray-600 transition-colors"
      >
        <ChevronRight size={16} />
      </button>
    );

    return buttons;
  };

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">
          {t("wallet.title", "My Wallet")}
        </h1>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        ) : (
          <>
            {/* Transaction Table */}
            <div className="bg-gray-800 rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-300">
                        {t("wallet.transactionId", "Transaction ID")}
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-300">
                        {t("wallet.date", "Date")}
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-300">
                        {t("wallet.itemDetails", "Items Details")}
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-300">
                        {t("wallet.amount", "Amount")}
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-300">
                        {t("wallet.status", "Status")}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {transactions.length > 0 ? (
                      transactions.map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-gray-750">
                          <td className="px-6 py-4 text-sm text-gray-300">
                            {transaction.id}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-300">
                            {formatDate(transaction.crtTime)}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-300">
                            {transaction.remark}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-300">
                            {formatAmount(transaction.money)}
                          </td>
                          <td className="px-6 py-4">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                                transaction.money
                              )}`}
                            >
                              Completed
                            </span>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={5}
                          className="px-6 py-12 text-center text-gray-400"
                        >
                          {t("wallet.noTransactions", "No transactions found")}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2 mt-6">
                {renderPaginationButtons()}
              </div>
            )}

            {/* Transaction Summary */}
            {transactions.length > 0 && (
              <div className="mt-4 text-sm text-gray-400 text-center">
                {t(
                  "wallet.showingResults",
                  "Showing {{start}} to {{end}} of {{total}} transactions",
                  {
                    start: (currentPage - 1) * pageSize + 1,
                    end: Math.min(currentPage * pageSize, totalTransactions),
                    total: totalTransactions,
                  }
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
