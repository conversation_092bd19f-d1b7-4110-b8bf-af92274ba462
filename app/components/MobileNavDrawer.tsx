import React from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { Link, useLocation } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { X } from "lucide-react";
import { MAIN_NAV_ITEMS } from "../constants/navigation";

interface MobileNavDrawerContentProps {
  onLinkClick?: () => void;
}

const MobileNavDrawerContent: React.FC<MobileNavDrawerContentProps> = ({
  onLinkClick,
}) => {
  const { t } = useTranslation();
  const location = useLocation();

  const isActiveRoute = (path: string) => {
    return location.pathname === path;
  };

  return (
    <Dialog.Portal>
      <Dialog.Overlay className="fixed inset-0 bg-black/70 z-[51] animate-in fade-in-0 md:hidden" />
      <Dialog.Content
        className="fixed pt-safe top-0 left-0 bottom-0 h-full w-[85%] max-w-[300px] 
                  bg-neutral-900 overflow-y-auto z-[52] outline-none shadow-lg
                  data-[state=open]:animate-slideInFromLeft data-[state=closed]:animate-slideOutToLeft
                  md:hidden"
      >
        <div className="flex flex-col h-full">
          {/* Drawer Header with Close Button */}
          <div className="flex justify-between items-center p-4 border-b border-neutral-800">
            <Dialog.Title className="text-lg font-medium text-white">
              <img
                src="/logo-horizontal.png"
                alt="logo"
                className="h-8 w-auto"
              />
            </Dialog.Title>
            <Dialog.Close className="text-white hover:text-gray-300">
              <X size={20} />
            </Dialog.Close>
          </div>

          {/* Navigation Links */}
          <div className="px-2 py-4 flex-grow">
            {MAIN_NAV_ITEMS.map((item) => {
              const active = isActiveRoute(item.path);
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`block px-3 py-3 rounded-md text-lg font-medium 
                              ${
                                active
                                  ? "bg-neutral-800 text-brand-red border-l-4 border-brand-red pl-2"
                                  : "text-white hover:bg-neutral-800"
                              }`}
                  onClick={onLinkClick}
                >
                  {t(item.label)}
                </Link>
              );
            })}
          </div>
        </div>
      </Dialog.Content>
    </Dialog.Portal>
  );
};

export default MobileNavDrawerContent;
