import React, { useState } from "react";
import { Menu } from "lucide-react";
import { Link, useLocation } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "./LanguageSwitcher";
import MobileNavDrawerContent from "./MobileNavDrawer";
import * as Dialog from "@radix-ui/react-dialog";
import { MAIN_NAV_ITEMS } from "../constants/navigation";
import Button from "./ui/Button";

interface AppHeaderTraceProps {
  onOpenClick?: () => void;
}

const AppHeaderTrace: React.FC<AppHeaderTraceProps> = ({ onOpenClick }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { t } = useTranslation();
  const location = useLocation();

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  const isActiveRoute = (path: string) => {
    return location.pathname === path;
  };

  return (
    <>
      <header className="fixed pt-safe top-0 left-0 right-0 bg-main-bg z-50 border-b border-neutral-800">
        <div className="mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            {/* Left side - Logo and Mobile Menu */}
            <div className="flex items-center">
              {/* Mobile menu button */}
              <Dialog.Root
                open={mobileMenuOpen}
                onOpenChange={setMobileMenuOpen}
              >
                <Dialog.Trigger asChild>
                  {/* <button
                    className="inline-flex items-center justify-center p-2 text-white md:hidden"
                    aria-label={t("nav.openMenu")}
                  >
                    <Menu size={24} />
                  </button> */}
                </Dialog.Trigger>
                <MobileNavDrawerContent onLinkClick={closeMobileMenu} />
              </Dialog.Root>

              {/* Logo - Placed outside Dialog.Root but adjacent in flex layout */}
              <div className="flex-shrink-0 flex items-center ml-2 md:ml-0">
                {/* <Link to="#" className="flex items-center"> */}
                <img
                  src="/logo-horizontal.png"
                  alt={"SnapDrama"}
                  className="h-8 w-auto cursor-pointer"
                />
                {/* </Link> */}
              </div>

              {/* Desktop navigation links - hidden on mobile */}
              <nav className="hidden md:ml-8 md:flex md:space-x-6">
                {/* TODO */}
                {/* {MAIN_NAV_ITEMS.map((item) => (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`px-2 py-1 text-sm font-medium transition-colors ${
                      isActiveRoute(item.path)
                        ? "text-brand-red border-b-2 border-brand-red"
                        : "text-white hover:text-gray-300"
                    }`}
                  >
                    {t(item.translationKey)}
                  </Link>
                ))} */}
              </nav>
            </div>

            {/* Right side - Language selector and User profile */}
            <div className="flex items-center space-x-4">
              {/* Language selector */}

              {/* User avatar */}
              {/* <Link to="/profile" className="flex items-center">
                <img
                  src="/images/profile-avatar.png"
                  alt={t("nav.profile") || "User Profile"}
                  className="w-8 h-8 border border-white rounded-full object-cover"
                />
              </Link> */}

              <div className="relative">
                <Button
                  variant="primary"
                  size="sm"
                  className="bg-brand-red rounded-3xl"
                  onClick={onOpenClick}
                >
                  {t("open")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* This div acts as a spacer to push content below the fixed header */}
      <div className="h-16 w-full"></div>
    </>
  );
};

export default AppHeaderTrace;
