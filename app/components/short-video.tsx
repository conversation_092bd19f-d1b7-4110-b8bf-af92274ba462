import {
  useState,
  useRef,
  type MouseEvent,
  useEffect,
  useCallback,
} from "react";
import { X, ShieldAlert } from "lucide-react";
import BaseVideoPlayer from "./base-video-player";
import VideoControls from "./video/VideoControls";
import { ShortVideoProps, defaultShortVideoProps } from "~/types/videos";
import { videoEvents } from "~/utils/analytics";
import { useTranslation } from "react-i18next";
import { useToast } from "./ui/toast-provider";

export default function ShortVideo(props: ShortVideoProps) {
  // Merge provided props with defaults
  const {
    videoUrl,
    subtitleUrl,
    posterUrl,
    profileImageUrl,
    username,
    title,
    description,
    tags,
    initialLikes,
    initialComments,
    initialShares,
    initialLiked,
    initialSaved,
    locked,
    onClose,
    onLike,
    onSave,
    onShare,
    onComment,
    onProfileClick,
    onVideoEnd,
    muted,
    isActive = true, // New prop to indicate if this video is currently active
  } = { ...defaultShortVideoProps, ...props };
  const { t } = useTranslation();
  const { showToast } = useToast();
  // --- State for UI elements and interactions   ---
  const [showControlsOverlay, setShowControlsOverlay] = useState(true);
  const [liked, setLiked] = useState(initialLiked ?? false);
  const [likes, setLikes] = useState(initialLikes ?? 0);
  const [comments, setComments] = useState(initialComments ?? 0);
  const [shares, setShares] = useState(initialShares ?? 0);
  const [saved, setSaved] = useState(initialSaved ?? false);
  const [showMore, setShowMore] = useState(false);
  const [showTopUpDrawer, setShowTopUpDrawer] = useState(false);

  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // --- Control Visibility Logic ---
  const hideControlsOverlayWithDelay = useCallback(() => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControlsOverlay(false);
    }, 3000);
  }, []);

  useEffect(() => {
    setShowControlsOverlay(true);
    hideControlsOverlayWithDelay();
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [hideControlsOverlayWithDelay]);

  // Show overlay and reset timer on interaction
  const handleInteraction = useCallback(
    (e?: MouseEvent) => {
      e?.stopPropagation();
      setShowControlsOverlay(true);
      hideControlsOverlayWithDelay();
    },
    [hideControlsOverlayWithDelay]
  );

  // --- Event Handlers for UI Actions --- //
  const handleLikeClick = (e: MouseEvent) => {
    handleInteraction(e);
    const newLikedState = !liked;
    const newLikesCount = newLikedState ? likes + 1 : likes - 1;
    setLiked(newLikedState);
    setLikes(newLikesCount);
    onLike?.(newLikedState, newLikesCount);
  };

  const handleSaveClick = (e: MouseEvent) => {
    handleInteraction(e);
    const newSavedState = !saved;
    setSaved(newSavedState);
    onSave?.(newSavedState);
  };

  const handleShareClick = (e: MouseEvent) => {
    handleInteraction(e);
    // copy current url
    navigator.clipboard.writeText(window.location.href);
    showToast(t("toast.copiedToClipboard"));
  };

  const handleCommentClick = (e: MouseEvent) => {
    handleInteraction(e);
    onComment?.();
  };

  const handleProfileClick = (e: MouseEvent) => {
    handleInteraction(e);
    onProfileClick?.();
  };

  const handleShowMoreClick = (e: MouseEvent) => {
    handleInteraction(e);
    setShowMore(!showMore);
  };

  const handleTopUpClick = () => {
    setShowTopUpDrawer(true);
  };

  return (
    <div
      className="w-full h-full relative flex items-center justify-center"
      onClick={!locked ? handleInteraction : undefined} // Only allow interaction if not locked
    >
      {/* Close Button */}

      {/* Base Video Player */}
      <BaseVideoPlayer
        src={videoUrl}
        poster={posterUrl}
        autoPlay={isActive} // Only autoplay if this video is active
        showControls={showControlsOverlay && !locked}
        showTitleDescription={showControlsOverlay && !locked}
        onEnded={onVideoEnd}
        className="w-full h-full"
        controlsBottomPadding="mb-12"
        title={title}
        description={description}
        // #TODO
        // tags={tags}
        isLocked={locked}
        subtitleUrl={subtitleUrl}
        muted={muted}
        isActive={isActive} // Pass isActive to BaseVideoPlayer
      />

      {/* Video controls (like, comment, share buttons) */}
      {showControlsOverlay && !locked && (
        <VideoControls
          likes={likes}
          comments={comments}
          shares={shares}
          liked={liked}
          saved={saved}
          profileImageUrl={profileImageUrl}
          username={username}
          onLikeClick={handleLikeClick}
          onCommentClick={handleCommentClick}
          onShareClick={handleShareClick}
          onSaveClick={handleSaveClick}
          onProfileClick={handleProfileClick}
        />
      )}

      {/* Placeholder for Top Up Drawer */}
      {showTopUpDrawer && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
          <div className="bg-neutral-800 p-6 rounded-lg text-white w-full max-w-sm">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">Top Up Coins</h3>
              <button
                onClick={() => setShowTopUpDrawer(false)}
                className="text-neutral-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>
            <p className="mb-4">Please top up your account to continue.</p>
            <div className="flex justify-center">
              <ShieldAlert size={48} className="text-yellow-500 mb-4" />
            </div>
            {/* Add actual top-up options here */}
            <button className="w-full bg-pink-600 hover:bg-pink-700 text-white font-semibold py-3 px-6 rounded-lg text-lg">
              Proceed to Top Up
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
