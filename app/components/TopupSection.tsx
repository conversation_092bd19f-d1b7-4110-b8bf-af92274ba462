import { Coins } from "lucide-react";
import { useNavigate } from "@remix-run/react";
import { useUser } from "~/context/auth-context";

interface TopupSectionProps {
  className?: string;
}

export default function TopupSection({ className = "" }: TopupSectionProps) {
  const navigate = useNavigate();
  const { userInfo, isLoggedIn } = useUser();

  const handleTopUpClick = () => {
    navigate("/user-settings/topup");
  };

  if (!isLoggedIn || !userInfo) {
    return null;
  }

  return (
    <div
      className={`bg-neutral-800 rounded-lg overflow-hidden relative ${className}`}
      style={{
        backgroundImage: "url(/images/top-up-bg.png)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/40 rounded-lg"></div>

      {/* Content */}
      <div className="relative z-10">
        {/* User Profile Header */}
        <div className="flex items-center space-x-3 p-4 bg-black/20 backdrop-blur-sm">
          {/* User Avatar */}
          <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">
              {userInfo.nickname?.charAt(0)?.toUpperCase() || "W"}
            </span>
          </div>

          {/* User Info */}
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="text-white font-medium text-sm">
                {userInfo.nickname || "Winston"}
              </span>
              {userInfo.vip && (
                <span className="bg-orange-500 text-white text-xs px-2 py-0.5 rounded-full font-medium">
                  VIP
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Account Balance */}
        <div className="px-4 py-3 bg-black/20 backdrop-blur-sm">
          <div className="text-gray-300 text-sm mb-1">Account Balance</div>
          <div className="flex items-center space-x-2">
            <Coins size={16} className="text-yellow-400" />
            <span className="text-white font-semibold">
              {userInfo.gold} Coins
            </span>
          </div>
        </div>

        {/* Top Up Button */}
        <div className="p-4">
          <button
            onClick={handleTopUpClick}
            className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
          >
            Top Up
          </button>
        </div>
      </div>
    </div>
  );
}
