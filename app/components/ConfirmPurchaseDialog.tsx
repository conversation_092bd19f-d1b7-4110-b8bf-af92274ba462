import * as Dialog from "@radix-ui/react-dialog";
import { X, ShoppingBag, CheckCircle } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PRICING } from "~/constants/currency";

interface ConfirmPurchaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentBalance: number;
  onConfirmPurchase: () => Promise<void>;
}

export default function ConfirmPurchaseDialog({
  open,
  onOpenChange,
  currentBalance,
  onConfirmPurchase,
}: ConfirmPurchaseDialogProps) {
  const { t } = useTranslation();
  const [isProcessing, setIsProcessing] = useState(false);
  const [purchaseSuccess, setPurchaseSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const unlockCost = PRICING.UNLOCK_EPISODE_COST;
  const newBalance = currentBalance - unlockCost;

  const handleConfirmPurchase = async () => {
    try {
      setIsProcessing(true);
      setError(null);
      await onConfirmPurchase();
      setPurchaseSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Purchase failed");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    if (!isProcessing) {
      onOpenChange(false);
      // Reset state when closing
      setTimeout(() => {
        setPurchaseSuccess(false);
        setError(null);
      }, 300);
    }
  };

  const handleDialogInteraction = (e: React.TouchEvent | React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
  };

  return (
    <Dialog.Root open={open} onOpenChange={handleClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/80 z-50" />
        <Dialog.Content
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-900 rounded-2xl p-6 w-[90vw] max-w-md z-50 text-white"
          onTouchStart={handleDialogInteraction}
          onTouchMove={handleDialogInteraction}
          onTouchEnd={handleDialogInteraction}
          onMouseDown={handleDialogInteraction}
        >
          {/* Close button */}
          <Dialog.Close asChild>
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
              aria-label="Close"
              disabled={isProcessing}
            >
              <X size={20} />
            </button>
          </Dialog.Close>

          {!purchaseSuccess ? (
            // Confirm Purchase Screen
            <div className="text-center">
              {/* Icon */}
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center">
                  <ShoppingBag size={32} className="text-white" />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <CheckCircle size={16} className="text-white" />
                  </div>
                </div>
              </div>

              {/* Title */}
              <h2 className="text-xl font-bold mb-2">
                {t("purchase.confirmTitle", "Confirm Purchase")}
              </h2>

              {/* Description */}
              <p className="text-gray-300 mb-4">
                {t(
                  "purchase.confirmDescription",
                  `You're about to spend ${unlockCost} Coins to unlock.`
                )}
              </p>

              {/* Balance Info */}
              <p className="text-sm text-gray-400 mb-6">
                {t("purchase.currentBalance", "Your current balance")}: {currentBalance}{" "}
                {t("purchase.coins", "Coins")}.
              </p>

              {/* Error Message */}
              {error && (
                <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg">
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
              )}

              {/* Continue Question */}
              <p className="text-white mb-6">
                {t("purchase.continueQuestion", "Do you want to continue?")}
              </p>

              {/* Confirm Button */}
              <button
                onClick={handleConfirmPurchase}
                disabled={isProcessing}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-150"
              >
                {isProcessing
                  ? t("purchase.processing", "Processing...")
                  : t("purchase.confirmButton", "Yes, Spend Coins")}
              </button>
            </div>
          ) : (
            // Success Screen
            <div className="text-center">
              {/* Success Icon */}
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle size={32} className="text-white" />
                </div>
              </div>

              {/* Success Title */}
              <h2 className="text-xl font-bold mb-2">
                {t("purchase.successTitle", "Purchase Successful!")}
              </h2>

              {/* Success Description */}
              <p className="text-gray-300 mb-4">
                {t(
                  "purchase.successDescription",
                  `You have successfully spent ${unlockCost} Coins.`
                )}
              </p>

              {/* New Balance */}
              <p className="text-sm text-gray-400 mb-6">
                {t("purchase.newBalance", "Your new balance is")} {newBalance}{" "}
                {t("purchase.coins", "Coins")}.
              </p>

              {/* Thank you message */}
              <p className="text-white mb-6">
                {t("purchase.thankYou", "Thank you for your purchase!")}
              </p>

              {/* OK Button */}
              <button
                onClick={handleClose}
                className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-150"
              >
                {t("purchase.okButton", "Ok")}
              </button>
            </div>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
