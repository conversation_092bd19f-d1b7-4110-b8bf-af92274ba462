import React, { MouseEvent } from "react";
import { Heart, Share2, MessageCircle, BookmarkIcon } from "lucide-react";
import { formatCount } from "~/utils/formatting";

interface VideoControlsProps {
  likes: number;
  comments: number;
  shares: number;
  liked: boolean;
  saved: boolean;
  profileImageUrl?: string;
  username?: string;
  onLikeClick: (e: MouseEvent) => void;
  onCommentClick: (e: MouseEvent) => void;
  onShareClick: (e: MouseEvent) => void;
  onSaveClick: (e: MouseEvent) => void;
  onProfileClick: (e: MouseEvent) => void;
}

/**
 * Side control buttons for video player (like, comment, share, etc.)
 */
const VideoControls: React.FC<VideoControlsProps> = ({
  likes,
  comments,
  shares,
  liked,
  saved,
  profileImageUrl = "https://placehold.co/100x100",
  username = "Username",
  onLikeClick,
  onCommentClick,
  onShareClick,
  onSaveClick,
  onProfileClick,
}) => {
  return (
    <div
      className="absolute right-4 bottom-32 flex flex-col items-center space-y-6 z-30"
      onClick={(e) => e.stopPropagation()} // Prevent clicks passing through
    >
      {/* Profile pic */}

      {/* <button
        className="relative block focus:outline-none focus:ring-2 focus:ring-white rounded-full"
        onClick={onProfileClick}
        aria-label={`View profile for ${username}`}
      >
        <div className="w-10 h-10 rounded-full bg-gray-300 border-2 border-white overflow-hidden">
          <img
            src={profileImageUrl}
            alt={username}
            className="w-full h-full object-cover"
          />
        </div>
      </button> */}

      {/* Like button */}
      {/* <div className="flex flex-col items-center">
        <button
          className="w-10 h-10 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-white rounded-full"
          onClick={onLikeClick}
          aria-pressed={liked}
          aria-label={liked ? "Unlike video" : "Like video"}
        >
          <Heart
            size={28}
            fill={liked ? "#ff2d55" : "none"}
            stroke={liked ? "#ff2d55" : "white"}
            strokeWidth={1.5}
          />
        </button>
        <span className="text-white text-xs font-semibold mt-1">
          {formatCount(likes)}
        </span>
      </div> */}

      {/* Comment button */}
      {/* <div className="flex flex-col items-center">
        <button
          className="w-10 h-10 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-white rounded-full"
          onClick={onCommentClick}
          aria-label="View comments"
        >
          <MessageCircle size={28} className="text-white" strokeWidth={1.5} />
        </button>
        <span className="text-white text-xs font-semibold mt-1">
          {formatCount(comments)}
        </span>
      </div> */}

      {/* Share button */}
      <div className="flex flex-col items-center">
        <button
          className="w-10 h-10 flex items-center justify-center focus:outline-none rounded-full"
          onClick={onShareClick}
          aria-label="Share video"
        >
          <Share2 size={28} className="text-white" strokeWidth={1.5} />
        </button>
      </div>

      {/* Save button */}
      {/* <div className="flex flex-col items-center">
        <button
          className="w-10 h-10 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-white rounded-full"
          onClick={onSaveClick}
          aria-pressed={saved}
          aria-label={saved ? "Unsave video" : "Save video"}
        >
          <BookmarkIcon
            size={28}
            fill={saved ? "#ffffff" : "none"}
            className="text-white"
            strokeWidth={1.5}
          />
        </button>
        <span className="text-white text-xs font-semibold mt-1">Save</span>
      </div> */}
    </div>
  );
};

export default VideoControls;
