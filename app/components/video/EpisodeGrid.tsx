import React, { useState, useEffect } from "react";
import { Lock } from "lucide-react";
import { EpisodeItem, VideoData } from "~/types/index";
import { useNavigate, useSearchParams } from "@remix-run/react";
import { videoEvents } from "~/utils/analytics";
import { episodeButtonStyles } from "~/styles/episodeButtonVariants";
import { useTranslation } from "react-i18next";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";

export const EPISODE_TABS = [
  { id: "1-50", label: "1-50" },
  { id: "51-100", label: "51-100" },
  { id: "all", label: "episode.allEpisodes" },
];

interface EpisodeGridProps {
  videoData: VideoData;
  episodes: EpisodeItem[];
  className?: string;
  onEpisodeSelect?: (episodeId: string) => void;
}

export default function EpisodeGrid({
  videoData,
  episodes,
  className = "",
  onEpisodeSelect,
}: EpisodeGridProps) {
  const [searchParams] = useSearchParams();
  const activeEpisodeNumber = searchParams.get("episodeId");
  const { t } = useTranslation();

  // Get episode number from the title or index
  const getEpisodeNumber = (episode: EpisodeItem): number => {
    if (episode.title) {
      // Try to parse a number from the title (e.g., "1.mp4" -> 1)
      const match = episode.title.match(/^(\d+)/);
      if (match) {
        return parseInt(match[1], 10);
      }
    }
    return 0; // Default if we can't determine the episode number
  };

  // Initialize activeTab based on the current episode
  const getInitialTab = () => {
    if (!activeEpisodeNumber) return "1-50";

    const activeEpisode = episodes.find(
      (episode) => episode.id === activeEpisodeNumber
    );
    if (!activeEpisode) return "1-50";

    const episodeNumber = getEpisodeNumber(activeEpisode);

    if (episodeNumber >= 51 && episodeNumber <= 100) {
      return "51-100";
    } else if (episodeNumber >= 1 && episodeNumber <= 50) {
      return "1-50";
    } else {
      return "all";
    }
  };

  const [activeTab, setActiveTab] = useState<string>(getInitialTab());

  // Update active tab when the active episode changes
  useEffect(() => {
    setActiveTab(getInitialTab());
  }, [activeEpisodeNumber]);

  const navigate = useVideoNavigation();
  const handleEpisodeSelect = (episodeId: string) => {
    const fromEpisode = episodes.find(
      (episode) => episode.id === activeEpisodeNumber
    );
    const toEpisode = episodes.find((episode) => episode.id === episodeId);
    onEpisodeSelect && onEpisodeSelect(episodeId);
    navigate.navigateToVideoPlay(videoData.id, episodeId);
    videoEvents.changeEpisode(
      videoData?.seriesTitle || "",
      Number(fromEpisode?.id) || 0,
      Number(toEpisode?.id) || 0,
      fromEpisode?.title || "",
      toEpisode?.title || ""
    );
  };

  // Filter episodes based on active tab
  const getFilteredEpisodes = (): EpisodeItem[] => {
    if (activeTab === "all") {
      return episodes;
    }

    return episodes.filter((episode) => {
      const episodeNumber = getEpisodeNumber(episode);
      if (activeTab === "1-50") {
        return episodeNumber >= 1 && episodeNumber <= 50;
      } else if (activeTab === "51-100") {
        return episodeNumber >= 51 && episodeNumber <= 100;
      }
      return false;
    });
  };

  const filteredEpisodes = getFilteredEpisodes();

  return (
    <div className={className}>
      {/* Tab Navigation */}
      <div className="flex border-b border-neutral-700">
        {EPISODE_TABS.map((tab) => (
          <button
            key={tab.id}
            className={`px-4 py-2 text-sm font-semibold transition-colors ${
              activeTab === tab.id
                ? "text-brand-red border-b-2 border-brand-red"
                : "text-neutral-400"
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.id === "all" ? t(tab.label) : tab.label}
          </button>
        ))}
      </div>

      {/* Episode Grid */}
      <div className={`grid grid-cols-5 gap-2 py-4 px-4 lg:px-0`}>
        {filteredEpisodes.map((episode, index) => {
          const isActive = episode.id === activeEpisodeNumber;

          return (
            <button
              key={`ep-${episode.id}`}
              className={episodeButtonStyles({
                variant: isActive
                  ? "active" // Active style takes precedence
                  : episode.vip
                  ? "locked" // Then locked style if not active
                  : "default", // Otherwise default
              })}
              onClick={() => handleEpisodeSelect(episode.id)}
            >
              EP {episode.title?.replace(".mp4", "") || episode.id}
              {episode.vip && (
                <span className="absolute top-1 right-1 text-xs">
                  <Lock size={12} className="text-gray-500" />
                </span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
}
