import type { ContentItem } from "~/types/index";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState } from "react";
import Button from "~/components/ui/Button";
import ContentItemCard from "~/components/ui/ContentItemCard";

interface TrendingRowProps {
  title: string;
  items: ContentItem[];
}

const ITEMS_PER_PAGE = 4;

const TrendingRow = ({ title, items }: TrendingRowProps) => {
  const [currentPage, setCurrentPage] = useState(0);

  if (!items || items.length === 0) {
    return null;
  }

  const totalPages = Math.ceil(items.length / ITEMS_PER_PAGE);

  const handlePrev = () => {
    setCurrentPage((prevPage) => Math.max(0, prevPage - 1));
  };

  const handleNext = () => {
    setCurrentPage((prevPage) => Math.min(totalPages - 1, prevPage + 1));
  };

  const startIndex = currentPage * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentItems = items.slice(startIndex, endIndex);

  return (
    <div className="mb-8 px-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white text-xl font-bold">{title}</h3>
        {totalPages > 1 && (
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrev}
              disabled={currentPage === 0}
              className="bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Previous page"
            >
              <ChevronLeft size={20} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNext}
              disabled={currentPage === totalPages - 1}
              className="bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Next page"
            >
              <ChevronRight size={20} />
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-2">
        {currentItems.map((item) => (
          <ContentItemCard key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};

export default TrendingRow;
