import React from "react";
import { User, Video, Settings, LogOut } from "lucide-react";
import { useNavigate } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { useUser } from "~/context/auth-context";
import ResponsiveDropdown, {
  type DropdownOption,
} from "./ui/ResponsiveDropdown";

interface UserProfileMenuProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const UserProfileMenu: React.FC<UserProfileMenuProps> = ({
  open,
  onOpenChange,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { showLogoutConfirm } = useUser();

  const handleLogout = () => {
    showLogoutConfirm();
  };

  const menuOptions: DropdownOption[] = [
    {
      id: "account",
      label: t("profile.myAccount") || "My Account",
      icon: <User size={18} />,
      action: () => navigate("/user-settings/my-account"),
    },
    {
      id: "videos",
      label: t("profile.myVideos") || "My Videos",
      icon: <Video size={18} />,
      action: () => navigate("/profile/videos"),
    },
    {
      id: "settings",
      label: t("profile.settings") || "Settings",
      icon: <Settings size={18} />,
      action: () => navigate("/user-settings/my-account"),
    },
    {
      id: "logout",
      label: t("profile.logout") || "Logout",
      icon: <LogOut size={18} />,
      action: handleLogout,
    },
  ];

  const trigger = (
    <button className="flex items-center space-x-1">
      <img
        src="/images/profile-avatar.png"
        alt={t("nav.profile") || "User Profile"}
        className="w-8 h-8 border border-white rounded-full object-cover"
      />
    </button>
  );

  return (
    <ResponsiveDropdown
      options={menuOptions}
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
      title={t("profile.title") || "Profile"}
    />
  );
};

export default UserProfileMenu;
