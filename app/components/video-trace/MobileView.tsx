import { Play } from "lucide-react";
import Button from "~/components/ui/Button";
import type { ViewProps } from "./types";

// ========== Mobile View Component ===========
const MobileView: React.FC<ViewProps> = ({
  videoDetails,
  t,
  handlePlayNow,
  posterImageUrl,
  displaySynopsis,
  showMoreSynopsis,
  setShowMoreSynopsis,
  synopsisLength,
  totalEpisodes,
}) => {
  const videoTitle = videoDetails.title || "The Last Of Us"; // Shorter for mobile maybe
  const seasonNumber = "1"; // Placeholder
  // For mobile, EP number usually refers to current, not total in that format. Use 1 as placeholder.
  const currentEpisodeDisplay = "1"; // Placeholder
  const releaseYear = "2025/06/23"; // Placeholder
  const tags = videoDetails.topic || ["Action", "Drama"]; // Example from mobile image

  return (
    <div className="text-white flex flex-col">
      {/* Video Poster Section */}
      <div
        className="w-full bg-main-bg relative cursor-pointer group"
        onClick={handlePlayNow}
      >
        <img
          src={posterImageUrl}
          alt={videoTitle}
          className="w-full object-contain h-3/4" // Changed to object-cover for potentially better mobile fit
        />
        <div className="absolute z-50 inset-0 bg-main-bg bg-opacity-30 group-hover:bg-opacity-10 flex items-center justify-center transition-opacity duration-300">
          <Play
            size={64}
            className="text-white opacity-80 group-hover:opacity-100 transform group-hover:scale-110 transition-all duration-300"
            fill="currentColor"
          />
        </div>
      </div>
      {/* Details Section */}
      <div className="fixed bottom-0 left-0 right-0 h-3/4 bg-gradient-to-t from-black  to-transparent pointer-events-none"></div>
      <div className="p-4 fixed bottom-0 flex-grow flex flex-col min-h-0 w-full text-white z-10">
        {/* Scrollable Content Area (Tags, Info, Synopsis) */}
        <div className="relative flex-1 overflow-y-auto mb-4">
          <div className="flex flex-wrap gap-2 mb-3">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-white/30 rounded-full text-xs text-white"
              >
                {tag}
              </span>
            ))}
          </div>
          <div className="text-sm mb-3">
            <span>{videoTitle}</span>
            <span className="mx-1">·</span>

            <span>
              {totalEpisodes} {t("common.episodesCap")}
            </span>
            {/* <span className="mx-1">·</span>
            <span>{releaseYear}</span> */}
          </div>
          <p className="text-sm text-gray-300 leading-relaxed">
            {displaySynopsis}
            {videoDetails.introduce &&
              videoDetails.introduce.length > synopsisLength && (
                <button
                  className="text-red-500 hover:underline ml-1 font-semibold"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowMoreSynopsis(!showMoreSynopsis);
                  }}
                >
                  {showMoreSynopsis ? t("video.showLess") : t("video.readMore")}
                </button>
              )}
          </p>
        </div>

        {/* Play Now Button - directly under the scrollable area */}
        <Button
          variant="primary"
          className="w-full bg-red-600 hover:bg-red-700 rounded-3xl"
          onClick={(e) => {
            e.stopPropagation();
            handlePlayNow();
          }}
          leftIcon={<Play size={20} className="fill-white" />}
        >
          {t("video.playNow")}
        </Button>
      </div>
      {/* Fade overlay for scrollable text content */}
    </div>
  );
};

export default MobileView;
