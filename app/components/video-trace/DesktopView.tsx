import { Play, Star } from "lucide-react";
import Button from "~/components/ui/Button";
import type { ViewProps } from "./types";

// ========== Desktop View Component ===========
const DesktopView: React.FC<ViewProps> = ({
  videoDetails,
  t,
  handlePlayNow,
  posterImageUrl,
  displaySynopsis,
  showMoreSynopsis,
  setShowMoreSynopsis,
  synopsisLength,
  totalEpisodes,
}) => {
  const videoTitle = videoDetails.title || "The Last Of Us Season 1";
  const seasonNumber = "1"; // Placeholder - Adjust based on actual data structure
  const releaseYear = "2025/06/23"; // Placeholder
  const rating = "7.5"; // Placeholder
  const tags = videoDetails.topic || ["Adventure", "Action", "Young Love"];

  return (
    <div className=" text-white flex flex-row">
      {/* Video Poster Section */}
      <div
        className="w-3/5 lg:w-2/3 xl:w-3/4 h-screen bg-main-bg flex items-center justify-center relative cursor-pointer group"
        onClick={handlePlayNow}
      >
        <img
          src={posterImageUrl}
          alt={videoTitle}
          className="w-full h-full object-contain"
        />
        <div className="absolute inset-0 bg-main-bg bg-opacity-30 group-hover:bg-opacity-10 flex items-center justify-center transition-opacity duration-300">
          <Play
            size={80}
            className="text-white opacity-80 group-hover:opacity-100 transform group-hover:scale-110 transition-all duration-300"
            fill="currentColor"
          />
        </div>
      </div>
      {/* Details Section */}
      <div className="w-2/5 lg:w-1/3 xl:w-1/4 bg-main-bg border-l-[1px] border-gray-800 p-8 flex flex-col">
        <div className="w-full max-w-md mx-auto">
          <h1 className="text-2xl lg:text-3xl font-bold mb-2">{videoTitle}</h1>
          <div className="flex items-center gap-2 text-sm text-gray-400 mb-3">
            {/* <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
            <span>{rating}</span>
            <span>|</span>
            <span>
              {t("video.season")} {seasonNumber}
            </span> */}
            {/* <span>·</span> */}
            <span>
              {totalEpisodes} {t("common.episodesCap")}
            </span>
            {/* <span>·</span>
            <span>{releaseYear}</span> */}
          </div>
          <p className="text-sm text-gray-300 mb-4 leading-relaxed">
            {displaySynopsis}
            {videoDetails.introduce &&
              videoDetails.introduce.length > synopsisLength && (
                <button
                  className="text-red-500 hover:underline ml-1 font-semibold"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowMoreSynopsis(!showMoreSynopsis);
                  }}
                >
                  {showMoreSynopsis ? t("video.showLess") : t("video.readMore")}
                </button>
              )}
          </p>
          <div className="flex flex-wrap gap-2 mb-6">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-white/30 rounded-full text-xs text-white"
              >
                {tag}
              </span>
            ))}
          </div>
          <Button
            variant="primary"
            className="w-full bg-red-600 hover:bg-red-700 rounded-3xl"
            onClick={(e) => {
              e.stopPropagation();
              handlePlayNow();
            }}
            leftIcon={<Play size={20} className="fill-white" />}
          >
            {t("video.playNow")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DesktopView;
