import { Link } from "@remix-run/react";
import { Instagram, Facebook, Twitter, Youtube } from "lucide-react"; // Using Youtube as a placeholder for G
import { useState } from "react"; // Added for accordion state

// Updated AppLogo for mobile footer (optional, could be different or simpler)
const AppLogo = () => (
  <div className="flex flex-col items-center space-y-2 mb-6">
    {/* Replace with your actual logo SVG or Image component */}
    <img src="/logo-horizontal.png" alt="SnapDrama Logo" className="w-60" />
  </div>
);

interface FooterLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
}

const FooterLink: React.FC<FooterLinkProps> = ({ to, children, className }) => (
  <Link
    to={to}
    className={`block py-2 text-gray-300 hover:text-white transition-colors ${
      className || ""
    }`}
  >
    {children}
  </Link>
);

// Accordion Item for sections
interface AccordionItemProps {
  title: string;
  children: React.ReactNode;
}

const AccordionItem: React.FC<AccordionItemProps> = ({ title, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-700">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex justify-between items-center py-3 text-left text-white font-semibold focus:outline-none"
      >
        <span>{title}</span>
        <span>{isOpen ? "-" : "+"}</span>
      </button>
      {isOpen && <div className="pt-2 pb-3 px-1">{children}</div>}
    </div>
  );
};

const MobileFooter = () => {
  const currentYear = new Date().getFullYear();

  // QR Code data - replace with actual image URLs
  const qrCodes = [
    {
      name: "Apple",
      // placeholder, replace with actual QR image path or component
      imageUrl: "/qr-apple-placeholder.png",
    },
    {
      name: "Android",
      // placeholder, replace with actual QR image path or component
      imageUrl: "/qr-android-placeholder.png",
    },
  ];

  return (
    // Show only on small screens, hide on md and larger
    <footer className="bg-main-bg text-gray-400 py-8 md:hidden">
      <div className="container mx-auto px-6">
        {/* Sections - using a more compact layout for mobile */}
        <div className="space-y-3 mb-6 text-center">
          <h3 className="font-semibold text-white text-lg">Discover</h3>
          <FooterLink to="/">Home</FooterLink>
          <FooterLink to="/genre">Genre</FooterLink>
          <FooterLink to="/popular">Popular</FooterLink>
          <FooterLink to="/watchlist">My Watchlist</FooterLink>
        </div>

        <hr className="border-gray-800 my-4" />

        <div className="space-y-3 mb-6 text-center">
          <h3 className="font-semibold text-white text-lg">About</h3>
          <FooterLink to="/contact">Contact Us</FooterLink>
          <FooterLink to="/feedback">Feedback</FooterLink>
        </div>

        <hr className="border-gray-800 my-4" />

        {/* Download QR Codes */}
        <div className="mb-8 text-center">
          <h3 className="font-semibold text-white text-lg mb-3">Download</h3>
          <div className="flex justify-center space-x-6">
            {qrCodes.map((qr) => (
              <div key={qr.name} className="text-center">
                <div className="w-28 h-28 bg-gray-700 flex items-center justify-center rounded-lg mb-2 mx-auto">
                  {/* Placeholder for QR Code Image */}
                  {/* <img src={qr.imageUrl} alt={`${qr.name} QR Code`} className="w-full h-full object-contain rounded-lg" /> */}
                  <span className="text-sm text-gray-300">
                    QR for {qr.name}
                  </span>
                </div>
                <p className="text-sm">{qr.name}</p>
              </div>
            ))}
          </div>
        </div>

        <hr className="border-gray-800 my-6" />

        {/* Copyright and Legal Links */}
        <div className="text-center text-xs space-y-3 mb-6">
          <p>&copy;SnapDrama {currentYear}</p>
          <div className="space-x-3">
            <FooterLink to="/privacy-policy" className="inline-block">
              Privacy policy
            </FooterLink>
            <FooterLink to="/terms-of-service" className="inline-block">
              Term of service
            </FooterLink>
            {/* <FooterLink to="/disclaimer" className="inline-block">Disclaimer</FooterLink> */}
            {/* Language link might be better in app settings or a dropdown */}
            <FooterLink to="/language" className="inline-block">
              Language
            </FooterLink>
          </div>
        </div>

        {/* Social Media Icons */}
        <div className="flex justify-center space-x-5 mb-8">
          <a href="#" className="text-gray-400 hover:text-white">
            <Instagram size={24} />
          </a>
          <a href="#" className="text-gray-400 hover:text-white">
            <Facebook size={24} />
          </a>
          <a href="#" className="text-gray-400 hover:text-white">
            <Twitter size={24} />
          </a>
          <a href="#" className="text-gray-400 hover:text-white">
            <Youtube size={24} /> {/* G icon replacement */}
          </a>
        </div>

        {/* Logo and tagline at the very bottom */}
        <AppLogo />
        <p className="text-xs text-center mt-2 px-4">
          This platform is a shared space. All resources are freely uploaded by
          users. If you encounter any copyright infringement, please contact us
          for removal.
        </p>
      </div>
    </footer>
  );
};

export default MobileFooter;
