import { useState } from "react";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  CardNumberElement,
  CardExpiryElement,
  CardCvcElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import * as Dialog from "@radix-ui/react-dialog";
import { X, CheckCircle, AlertCircle } from "lucide-react";
import { useTranslation } from "react-i18next";
import { getStripePublishableKey } from "~/config/stripe";

interface StripePaymentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  clientSecret: string;
  amount: number;
  currency: string;
  onPaymentSuccess: () => void;
  onPaymentError: (error: Error) => void;
}

interface PaymentFormProps {
  clientSecret: string;
  amount: number;
  currency: string;
  onPaymentSuccess: () => void;
  onPaymentError: (error: Error) => void;
  onClose: () => void;
}

const PaymentForm = ({
  clientSecret,
  amount,
  currency,
  onPaymentSuccess,
  onPaymentError,
  onClose,
}: PaymentFormProps) => {
  const { t } = useTranslation();
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [cardholderName, setCardholderName] = useState("");
  const [country, setCountry] = useState("Malaysia");

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setPaymentError(null); // Clear any previous errors

    try {
      const cardNumberElement = elements.getElement(CardNumberElement);

      if (!cardNumberElement) {
        throw new Error("Card element not found");
      }

      const { error, paymentIntent } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: {
            card: cardNumberElement,
            billing_details: {
              name: cardholderName,
              address: {
                country: "MY", // Malaysia
              },
            },
          },
        }
      );

      if (error) {
        throw new Error(error.message || "Payment failed");
      }

      if (paymentIntent && paymentIntent.status === "succeeded") {
        setPaymentSuccess(true);
        onPaymentSuccess();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Payment failed";
      setPaymentError(errorMessage);
      onPaymentError(error as Error);
    } finally {
      setIsProcessing(false);
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-MY", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: "16px",
        color: "#ffffff",
        "::placeholder": {
          color: "#9ca3af",
        },
        backgroundColor: "transparent",
      },
      invalid: {
        color: "#ef4444",
      },
    },
  };

  // Show success state if payment is successful
  if (paymentSuccess) {
    return (
      <div className="bg-gray-900 text-white p-8 rounded-lg max-w-lg w-full mx-auto">
        <div className="text-center">
          <div className="mb-6">
            <CheckCircle size={80} className="text-green-500 mx-auto" />
          </div>
          <h2 className="text-2xl font-bold mb-4 text-green-400">
            {t("payment.paymentSuccessful", "Payment Successful!")}
          </h2>
          <div className="text-left bg-gray-800 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold mb-3 text-white">
              {t("payment.transactionDetails", "Transaction Details")}
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">
                  {t("payment.amount", "Amount")}:
                </span>
                <span className="text-white font-medium">
                  {formatAmount(amount, currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">
                  {t("payment.status", "Status")}:
                </span>
                <span className="text-green-400 font-medium">
                  {t("payment.completed", "Completed")}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">
                  {t("payment.paymentMethod", "Payment Method")}:
                </span>
                <span className="text-white">
                  {t("payment.creditCard", "Credit Card")}
                </span>
              </div>
            </div>
          </div>
          <p className="text-gray-300 mb-6 text-sm">
            {t(
              "payment.successDescription",
              "Your payment has been processed successfully. Your coins have been added to your account and you can start using them immediately."
            )}
          </p>
          <button
            onClick={onClose}
            className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
          >
            {t("payment.continue", "Continue")}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className=" text-white p-6 w-[400px] max-w-lg mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">
          {t("payment.cardPayment", "Card Payment")}
        </h2>
      </div>

      {/* Error Display */}
      {paymentError && (
        <div className="mb-4 p-4 bg-red-900/50 border border-red-500 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <AlertCircle className="w-5 h-5 text-red-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-400 mb-1">
                {t("payment.paymentFailed", "Payment Failed")}
              </h3>
              <p className="text-sm text-red-300">{paymentError}</p>
            </div>
            <button
              onClick={() => setPaymentError(null)}
              className="flex-shrink-0 text-red-400 hover:text-red-300"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Card User Name */}
        <div>
          <label className="block text-sm text-gray-400 mb-2">
            {t("payment.cardUserName", "Card User Name")}
          </label>
          <input
            type="text"
            value={cardholderName}
            onChange={(e) => setCardholderName(e.target.value)}
            placeholder={t("payment.enterFullName", "Enter your full name")}
            className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            required
          />
        </div>

        {/* Card Number */}
        <div>
          <label className="block text-sm text-gray-400 mb-2">
            {t("payment.cardNumber", "Card Number")}
          </label>
          <div className="relative">
            <div className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 flex items-center">
              <div className="flex-1">
                <CardNumberElement options={cardElementOptions} />
              </div>
              <div className="flex space-x-1 ml-2">
                <img
                  src="/icons/visa.png"
                  alt="Visa"
                  className="h-6 w-auto"
                  onError={(e) => {
                    e.currentTarget.style.display = "none";
                  }}
                />
                <img
                  src="/icons/mastercard.png"
                  alt="Mastercard"
                  className="h-6 w-auto"
                  onError={(e) => {
                    e.currentTarget.style.display = "none";
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Expiration Date and Security Code */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">
              {t("payment.expirationDate", "Expiration Date")}
            </label>
            <div className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3">
              <CardExpiryElement options={cardElementOptions} />
            </div>
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-2">
              {t("payment.securityCode", "Security Code")}
            </label>
            <div className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3">
              <CardCvcElement options={cardElementOptions} />
            </div>
          </div>
        </div>

        {/* Country */}
        <div>
          <label className="block text-sm text-gray-400 mb-2">
            {t("payment.country", "Country")}
          </label>
          <select
            value={country}
            onChange={(e) => setCountry(e.target.value)}
            className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500"
          >
            <option value="Malaysia">Malaysia</option>
            <option value="Singapore">Singapore</option>
            <option value="Thailand">Thailand</option>
            <option value="Indonesia">Indonesia</option>
          </select>
        </div>

        {/* Pay Button */}
        <button
          type="submit"
          disabled={!stripe || isProcessing || paymentSuccess}
          className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 mt-6"
        >
          {isProcessing
            ? t("payment.processing", "Processing...")
            : paymentSuccess
            ? t("payment.paymentComplete", "Payment Complete!")
            : `${t("payment.pay", "Pay")} ${formatAmount(amount, currency)}`}
        </button>
      </form>
    </div>
  );
};

export const WrappedElementsPaymentForm = (props: PaymentFormProps) => {
  const stripePublishableKey = getStripePublishableKey();
  const [stripePromise] = useState(() => loadStripe(stripePublishableKey));
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm {...props} />
    </Elements>
  );
};

const StripePaymentDialog = ({
  isOpen,
  onClose,
  clientSecret,
  amount,
  currency,
  onPaymentSuccess,
  onPaymentError,
}: StripePaymentDialogProps) => {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[80]" />
        <Dialog.Content className="fixed inset-0 z-[81] overflow-y-auto outline-none">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="bg-[#1F2937] rounded-2xl shadow-2xl max-h-[90vh] overflow-y-auto text-white relative">
              <Dialog.Close className="absolute top-4 right-4 text-gray-400 hover:text-white z-10">
                <X size={24} />
              </Dialog.Close>

              <WrappedElementsPaymentForm
                clientSecret={clientSecret}
                amount={amount}
                currency={currency}
                onPaymentSuccess={onPaymentSuccess}
                onPaymentError={onPaymentError}
                onClose={onClose}
              />
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default StripePaymentDialog;
