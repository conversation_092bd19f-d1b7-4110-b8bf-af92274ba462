import { useState } from "react";
import {
  CardElement,
  useStripe,
  useElements,
  Elements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import type { StripeCardElementOptions } from "@stripe/stripe-js";
import { Button } from "~/components/ui/Button";
import { createBaseApi } from "~/utils/base-service";

interface PaymentFormProps {
  amount: number;
  currency?: string;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: Error) => void;
}

// Card element styling
const cardElementOptions: StripeCardElementOptions = {
  style: {
    base: {
      color: "#fff",
      fontFamily: '"Inter", sans-serif',
      fontSmoothing: "antialiased",
      fontSize: "16px",
      "::placeholder": {
        color: "#aab7c4",
      },
    },
    invalid: {
      color: "#fa755a",
      iconColor: "#fa755a",
    },
  },
  hidePostalCode: true,
};

// The actual payment form component
const PaymentForm = ({
  amount,
  currency = "usd",
  onSuccess,
  onError,
}: PaymentFormProps) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet. Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      // For demo purposes, we'll use a conditional to either use the real API or simulate a payment
      const USE_REAL_API = true; // Set to true to use the real API

      if (USE_REAL_API) {
        // Create a payment intent using the backend API
        const api = createBaseApi();
        const response = await api.createOrder({
          amount,
          currency,
        });

        if (!response.ok) {
          throw new Error(response.msg || "Failed to create payment intent");
        }

        // The client secret is returned directly in the data field
        const clientSecret = response.data;

        const cardElement = elements.getElement(CardElement);

        if (!cardElement) {
          throw new Error("Card element not found");
        }

        const { error, paymentIntent } = await stripe.confirmCardPayment(
          clientSecret,
          {
            payment_method: {
              card: cardElement,
              billing_details: {
                name: "Test User",
              },
            },
          }
        );

        if (error) {
          setPaymentError(
            error.message || "An error occurred during payment processing"
          );
          if (onError) {
            onError(new Error(error.message || "Payment failed"));
          }
        } else if (paymentIntent.status === "succeeded") {
          setPaymentSuccess(true);
          if (onSuccess) {
            onSuccess(paymentIntent);
          }
        }
      } else {
        // Simulate a successful payment for demo purposes
        setTimeout(() => {
          setPaymentSuccess(true);
          setIsProcessing(false);
          if (onSuccess) {
            onSuccess({ id: "test_payment_intent_id" });
          }
        }, 2000);
      }
    } catch (error) {
      console.error("Payment error:", error);
      setPaymentError(
        error instanceof Error
          ? error.message
          : "An error occurred during payment processing"
      );
      if (onError && error instanceof Error) {
        onError(error);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const formattedAmount = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase(),
  }).format(amount / 100);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Card Details
        </label>
        <div className="p-3 border border-gray-600 rounded-md bg-gray-800/50">
          <CardElement options={cardElementOptions} />
        </div>
      </div>

      {paymentError && (
        <div className="p-3 bg-red-900/50 border border-red-700 rounded-md text-red-200">
          {paymentError}
        </div>
      )}

      {paymentSuccess && (
        <div className="p-3 bg-green-900/50 border border-green-700 rounded-md text-green-200">
          Payment successful! Thank you for your purchase.
        </div>
      )}

      <Button
        type="submit"
        disabled={!stripe || isProcessing || paymentSuccess}
        className="w-full py-3"
      >
        {isProcessing
          ? "Processing..."
          : paymentSuccess
          ? "Paid"
          : `Pay ${formattedAmount}`}
      </Button>
    </form>
  );
};

// Wrapper component that provides the Stripe context
interface StripePaymentFormProps extends PaymentFormProps {
  stripePublishableKey: string;
}

const StripePaymentForm = ({
  stripePublishableKey,
  ...props
}: StripePaymentFormProps) => {
  const [stripePromise] = useState(() => loadStripe(stripePublishableKey));

  return (
    <Elements stripe={stripePromise}>
      <PaymentForm {...props} />
    </Elements>
  );
};

export default StripePaymentForm;
