# Stripe Payment Integration

This directory contains components and utilities for integrating Stripe payments into the SnapDrama application.

## Setup

1. Make sure you have the following environment variable set:

   - `NODE_ENV`: Set to 'dev' or 'production' (defaults to 'dev')

2. Stripe keys are configured in `app/config/stripe.ts` based on the environment

3. Required packages:
   - `@stripe/stripe-js`: Stripe JavaScript SDK
   - `@stripe/react-stripe-js`: React components for Stripe

## Components

### StripePaymentForm

A reusable payment form component that handles credit card payments using Stripe Elements.

#### Usage

```tsx
import StripePaymentForm from "~/components/payment/StripePaymentForm";

// In your component
return (
  <StripePaymentForm
    stripePublishableKey={stripePublishableKey}
    amount={1000} // Amount in cents (e.g., $10.00)
    currency="usd" // Currency code
    onSuccess={(paymentIntent) => {
      // Handle successful payment
      console.log("Payment successful:", paymentIntent);
    }}
    onError={(error) => {
      // Handle payment error
      console.error("Payment error:", error);
    }}
  />
);
```

## Backend API Integration

The payment form uses the existing backend API endpoint `/auth/createOrder` to create a payment intent.

### Request

```
POST /auth/createOrder
Content-Type: application/json
Authorization: Bearer <token> (if user is logged in)

{
  "amount": 1000,
  "currency": "usd"
}
```

### Response

```json
{
  "data": "pi_3RRqUf4cIGELziOf1wBhHC7J_secret_MbOjkA096wOFi5VU66kOH2ycr",
  "code": 200,
  "crypt": false,
  "ok": true,
  "msg": "注册成功",
  "timestamp": 1747987045063
}
```

The `data` field contains the Stripe client secret that is used to confirm the payment on the client side.

## Test Cards

For testing, you can use the following test card numbers:

- **Successful payment**: 4242 4242 4242 4242
- **Authentication required**: 4000 0025 0000 3155
- **Payment declined**: 4000 0000 0000 9995

For all test cards:

- Use any future date for expiration
- Use any 3-digit CVC
- Use any 5-digit postal code

## Enabling Real Payments

The payment form is configured to use the real API by default. If you want to use simulated payments for testing:

1. Set the `USE_REAL_API` constant to `false` in the `StripePaymentForm` component.
2. This will simulate a successful payment without making any API calls.

## Going to Production

Before going to production:

1. Switch from Stripe test keys to live keys
2. Implement proper error handling and logging
3. Add additional security measures (e.g., idempotency keys)
4. Consider implementing webhooks for asynchronous payment events
5. Add proper receipt/invoice generation
