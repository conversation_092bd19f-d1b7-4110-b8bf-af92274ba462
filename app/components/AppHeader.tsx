import React, { useState } from "react";
import { User, Search, Menu } from "lucide-react";
import { Link } from "@remix-run/react";
import LanguageSwitcher from "./LanguageSwitcher";
import MobileNavDrawerContent from "./MobileNavDrawer";
import UserProfileMenu from "./UserProfileMenu";
import * as Dialog from "@radix-ui/react-dialog";
import { useUser } from "~/context/auth-context";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import LoginDialog from "./auth/LoginDialog";
import RegisterDialog from "./auth/RegisterDialog";
import { MAIN_NAV_ITEMS } from "~/constants/navigation";
import { useTranslation } from "react-i18next";

const AppHeader: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const [loginDialogOpen, setLoginDialogOpen] = useState(false);
  const [registerDialogOpen, setRegisterDialogOpen] = useState(false);
  const { isLoggedIn } = useUser();
  const { isDesktop } = useMediaQuery();
  const { t } = useTranslation();
  const handleOpenRegister = () => {
    setLoginDialogOpen(false);
    setRegisterDialogOpen(true);
  };

  const handleOpenLogin = () => {
    setRegisterDialogOpen(false);
    setLoginDialogOpen(true);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };
  const isActiveRoute = (path: string) => {
    return false;
  };
  return (
    <>
      <header className="fixed pt-safe top-0 left-0 right-0 bg-main-bg z-50 border-b border-neutral-800">
        <div className="mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            {/* Left side - Logo and Mobile Menu */}
            <div className="flex items-center">
              {/* Mobile menu button */}
              <Dialog.Root
                open={mobileMenuOpen}
                onOpenChange={setMobileMenuOpen}
              >
                <Dialog.Trigger asChild>
                  <button
                    className="inline-flex items-center justify-center p-2 text-white md:hidden"
                    aria-label={t("nav.openMenu")}
                  >
                    <Menu size={24} />
                  </button>
                </Dialog.Trigger>
                <MobileNavDrawerContent onLinkClick={closeMobileMenu} />
              </Dialog.Root>

              {/* Logo - Placed outside Dialog.Root but adjacent in flex layout */}
              <div className="flex-shrink-0 flex items-center ml-2 md:ml-0">
                <Link to="/" className="flex items-center">
                  <img
                    src="/logo-horizontal.png"
                    alt={"SnapDrama"}
                    className="h-8 w-auto cursor-pointer"
                  />
                </Link>
              </div>

              {/* Desktop navigation links - hidden on mobile */}
              <nav className="hidden md:ml-8 md:flex md:space-x-12">
                {MAIN_NAV_ITEMS.map((item) => (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={
                      "px-2 py-1 text-sm font-medium text-white hover:text-gray-300 transition-colors"
                    }
                  >
                    {item.label}
                  </Link>
                ))}
              </nav>
            </div>

            {/* Right side - Search, Language selector and User profile */}
            <div className="flex items-center space-x-4">
              {/* Search icon */}
              <Link
                to="/search"
                className="flex items-center text-white hover:text-gray-300"
              >
                <Search size={24} />
              </Link>

              {/* Language selector */}

              {/* User avatar - only shown when logged in */}
              {isLoggedIn ? (
                <UserProfileMenu
                  open={profileMenuOpen}
                  onOpenChange={setProfileMenuOpen}
                />
              ) : isDesktop ? (
                <>
                  <button
                    onClick={() => setLoginDialogOpen(true)}
                    className="flex items-center text-white hover:text-gray-300"
                  >
                    <User size={24} />
                  </button>
                  <LoginDialog
                    open={loginDialogOpen}
                    onOpenChange={setLoginDialogOpen}
                    onOpenRegister={handleOpenRegister}
                  />
                  <RegisterDialog
                    open={registerDialogOpen}
                    onOpenChange={setRegisterDialogOpen}
                    onOpenLogin={handleOpenLogin}
                  />
                </>
              ) : (
                <Link
                  to="/login"
                  className="flex items-center text-white hover:text-gray-300"
                >
                  <User size={24} />
                </Link>
              )}

              <div className="relative">
                <LanguageSwitcher />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* This div acts as a spacer to push content below the fixed header */}
      <div className="h-16 w-full"></div>
    </>
  );
};

export default AppHeader;
