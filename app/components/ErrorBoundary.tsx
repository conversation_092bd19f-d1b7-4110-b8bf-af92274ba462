import { useRouteError, isRouteErrorResponse } from "@remix-run/react";
import { useTranslation } from "react-i18next";

export function ErrorBoundary() {
  const error = useRouteError();
  const { t } = useTranslation();

  if (isRouteErrorResponse(error)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">
            {error.status}
          </h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">
            {error.statusText}
          </h2>
          <p className="text-gray-600 mb-8">
            {error.data?.message || t("error.defaultMessage")}
          </p>
          <a
            href="/"
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t("error.backToHome")}
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center p-8">
        <h1 className="text-6xl font-bold text-gray-900 mb-4">500</h1>
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">
          {t("error.internalServerError")}
        </h2>
        <p className="text-gray-600 mb-8">{t("error.somethingWentWrong")}</p>
        <a
          href="/"
          className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          {t("error.backToHome")}
        </a>
      </div>
    </div>
  );
}
