import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLoaderData, useLocation, useNavigate } from "@remix-run/react";
import { type SelectOption } from "./ui/DrawerSelect";
import ResponsiveDropdown, {
  type DropdownOption,
} from "./ui/ResponsiveDropdown";
import { languageEvents } from "~/utils/analytics";
import {
  languageOptions as prefilterLanguageOptions,
  getLanguageName,
} from "~/utils/languageOptions";
import { createBaseApi } from "~/utils/base-service";
import { changeLanguage } from "~/utils/languageUtils";

// Define the type based on what we know is returned from the loader
interface LoaderData {
  locale?: string;
}

const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [languageOptions, setLanguageOptions] = useState<DropdownOption[]>([]);
  const [open, setOpen] = useState(false);

  const loaderData = useLoaderData<LoaderData>();
  const locale = loaderData?.locale;

  useEffect(() => {
    const fetchLanguages = async () => {
      setLoading(true);
      const response = await createBaseApi().getHomeLang();
      const filteredLanguages = prefilterLanguageOptions
        .filter((option) =>
          response.data.some((item) => item.id.toLowerCase() === option.country)
        )
        .map((option) => ({
          id: option.id,
          country: option.country,
          icon: option.icon,
          label: getLanguageName(option.id),
        }));
      setLanguageOptions(filteredLanguages);
      setLoading(false);
    };

    fetchLanguages();
  }, []);

  const handleLanguageChange = (option: DropdownOption) => {
    if (!option || !option.id) return;

    changeLanguage({
      newLanguage: option.id,
      i18n,
      location,
      navigate,
    });
  };

  const resolvedSelectedId =
    languageOptions.find((opt) => opt.id === locale)?.id ||
    languageOptions.find((opt) => opt.id === i18n.language)?.id ||
    languageOptions.find((opt) => opt.id === i18n.language.split("-")[0])?.id ||
    languageOptions.find(
      (opt) => opt.id === (i18n.options.fallbackLng as string)
    )?.id ||
    (languageOptions.length > 0 ? languageOptions[0].id : "");

  const trigger = (
    <button className="flex items-center space-x-1 text-white bg-transparent p-1 rounded-md hover:bg-neutral-800 transition-colors">
      <span>
        {loading || !languageOptions.length ? (
          <>
            {/* skeleton */}
            <div className="w-8 h-8 bg-neutral-700 rounded-full animate-pulse"></div>
          </>
        ) : (
          languageOptions.find((option) => option.id === resolvedSelectedId)
            ?.icon || languageOptions[0]?.icon
        )}
      </span>
    </button>
  );

  return (
    <ResponsiveDropdown
      options={languageOptions.map((option) => ({
        ...option,
        action: () => handleLanguageChange(option),
      }))}
      open={open}
      onOpenChange={setOpen}
      trigger={trigger}
      title={t("language.title") as string}
    />
  );
};

export default LanguageSwitcher;
