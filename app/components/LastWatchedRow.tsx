import { useState } from "react";
import type { ContentItem } from "~/types/index";
import {
  Play,
  PlusCircle,
  Star,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";
import Button from "~/components/ui/Button";

interface LastWatchedRowProps {
  title: string;
  items: ContentItem[];
}

const LastWatchedRow = ({ title, items }: LastWatchedRowProps) => {
  const { t } = useTranslation();
  const { navigateToVideo } = useVideoNavigation();
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!items || items.length === 0) {
    return null;
  }

  const currentItem = items[currentIndex];

  if (!currentItem) {
    return null;
  }

  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? items.length - 1 : prevIndex - 1
    );
    setShowFullDescription(false);
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === items.length - 1 ? 0 : prevIndex + 1
    );
    setShowFullDescription(false);
  };

  const description =
    currentItem.description ||
    "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.";
  const displayDescription = showFullDescription
    ? description
    : `${description.substring(0, 150)}${
        description.length > 150 ? "..." : ""
      }`;

  // Mock data or fallbacks for fields not in ContentItem
  const season = 1; // Default to Season 1 as item.season is not available
  const year = currentItem.year || new Date().getFullYear();
  const releaseDate = `${year}/01/01`; // Placeholder date using year
  const tags = Array.isArray(currentItem.genre)
    ? currentItem.genre
    : currentItem.genre
    ? [currentItem.genre]
    : ["Action", "Drama"];

  return (
    <div className="mb-12 px-4 md:px-0 text-white">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold">{title}</h3>
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            aria-label="Previous"
            onClick={handlePrev}
            disabled={items.length <= 1}
            className="bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-full transition-colors disabled:opacity-50"
          >
            <ChevronLeft size={20} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            aria-label="Next"
            onClick={handleNext}
            disabled={items.length <= 1}
            className="bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-full transition-colors disabled:opacity-50"
          >
            <ChevronRight size={20} />
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row rounded-lg overflow-hidden shadow-xl">
        {/* Left: Image */}
        <div className="w-full md:w-2/5 lg:w-1/3 xl:w-2/5 flex-shrink-0 h-64 md:h-auto relative">
          <img
            src={currentItem.thumbnailUrl}
            alt={currentItem.name}
            className="w-full h-full object-cover cursor-pointer transition-transform duration-300 ease-in-out group-hover:scale-105"
            onClick={() => navigateToVideo(currentItem.id)}
            loading="lazy"
          />
          {/* Subtle overlay to make text more readable if needed, or for Netflix logo */}
          <div className="absolute top-4 left-4">
            {/* Example Netflix-like logo - replace with actual if available */}
            <svg
              width="24"
              height="36"
              viewBox="0 0 24 36"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M20.9992 0.0107422H16.3184L10.7512 10.7783V0.0107422H6.07031V17.9891H10.6023L16.4168 6.82599V17.9891H21.1465L20.9992 0.0107422Z"
                fill="#E50914"
              />
              <path
                d="M10.7512 10.7783L16.3184 0.0107422H20.9992L10.6023 17.9891H6.07031V0.0107422H10.7512V10.7783Z"
                fill="#E50914"
              />{" "}
              {/* M part */}
              <path
                d="M11.7312 17.5256C11.4248 17.7041 11.0952 17.8401 10.7512 17.9298V35.9894H15.432V23.4102L11.7312 17.5256Z"
                fill="#E50914"
              />
            </svg>
          </div>
        </div>

        {/* Right: Content Details */}
        <div className="p-6 md:p-8 flex flex-col justify-between flex-grow">
          <div>
            <div className="flex flex-wrap gap-2 mb-3">
              {tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-neutral-700 text-neutral-300 rounded-full text-xs font-medium"
                >
                  {tag}
                </span>
              ))}
            </div>

            <h2
              className="text-3xl md:text-4xl font-bold mb-2 cursor-pointer hover:text-gray-300"
              onClick={() => navigateToVideo(currentItem.id)}
            >
              {currentItem.name}
            </h2>

            <div className="flex items-center space-x-3 text-neutral-400 text-sm mb-4">
              {currentItem.rating && (
                <div className="flex items-center">
                  <Star
                    size={16}
                    className="text-yellow-400 fill-yellow-400 mr-1"
                  />
                  <span>{currentItem.rating.toFixed(1)}</span>
                </div>
              )}
              <span>Season {season}</span>
              {currentItem.episodes && (
                <span>Episodes {currentItem.episodes}</span>
              )}
              <span>{releaseDate}</span>
            </div>

            <p className="text-neutral-300 text-sm mb-4 leading-relaxed">
              {displayDescription}
              {description.length > 150 && (
                <button
                  onClick={() => setShowFullDescription(!showFullDescription)}
                  className="text-brand-red hover:text-red-400 ml-1 font-semibold"
                >
                  {showFullDescription
                    ? t("common.readLess")
                    : t("common.readMore")}
                </button>
              )}
            </p>
          </div>

          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 mt-auto">
            <Button
              variant="primary"
              size="md"
              onClick={(e) => {
                e.stopPropagation();
                navigateToVideo(currentItem.id, { play: true });
              }}
              leftIcon={<Play size={20} />}
              className="w-full sm:w-auto"
            >
              {t("video.playNow")}
            </Button>
            <Button
              variant="outline"
              size="md"
              onClick={(e) => {
                e.stopPropagation();
                console.log("Add to list:", currentItem.name);
                // Add to list functionality here
              }}
              leftIcon={<PlusCircle size={20} />}
              className="w-full sm:w-auto border-neutral-600 text-neutral-300 hover:bg-neutral-700 hover:border-neutral-500"
            >
              {t("video.addToList")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LastWatchedRow;
