// BUG: if you see an error about a `Top Level await` import form /cjs instead
// see: <https://github.com/i18next/i18next-fs-backend/issues/57>
// import Backend from 'i18next-fs-backend';
import Backend from "i18next-fs-backend/cjs";
import { resolve } from "node:path";
import { RemixI18Next } from "remix-i18next/server";
import { DEFAULT_LANGUAGE, LANGUAGE_STORAGE_KEY, supportedLngs } from "./i18n";
import { createCookie } from "@remix-run/node";
import Cookies from "universal-cookie";

const i18next = new RemixI18Next({
  detection: {
    supportedLanguages: supportedLngs,
    fallbackLanguage: DEFAULT_LANGUAGE,
    // Enable cookie detection in the server
    order: ["searchParams", "custom", "header"],

    findLocale: async (request) => {
      const cookieHeader = request.headers.get("cookie");
      const cookie = new Cookies(cookieHeader);
      return cookie.get(LANGUAGE_STORAGE_KEY);
    },
    searchParamKey: "lng",
  },
  // Extending our default config file with server only fields
  i18next: {
    backend: {
      loadPath: resolve("./public/locales/{{lng}}/{{ns}}.json"),
    },
  },
  // Setting our our backend to load our translations from the file system via
  // i18next-fs-backend
  plugins: [Backend],
});

// Export both i18next instance and the cookie for use in routes

export default i18next;
