import { create } from "zustand";
import { persist } from "zustand/middleware";
import { languageOptions } from "~/utils/languageOptions";

interface LanguageState {
  // For app-wide language
  appLanguage: string;
  // For video-specific language
  videoLanguage: string;
  // Actions
  setAppLanguage: (lang: string) => void;
  setVideoLanguage: (lang: string) => void;
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set) => ({
      // Initialize with first language as default
      appLanguage: languageOptions[0].id,
      videoLanguage: languageOptions[0].id,

      setAppLanguage: (lang: string) => set({ appLanguage: lang }),
      setVideoLanguage: (lang: string) => set({ videoLanguage: lang }),
    }),
    {
      name: "language-storage", // name of the item in storage
      partialize: (state) => ({
        appLanguage: state.appLanguage,
        videoLanguage: state.videoLanguage,
      }),
    }
  )
);
