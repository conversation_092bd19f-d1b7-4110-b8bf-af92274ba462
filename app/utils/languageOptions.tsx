import { type SelectOption } from "~/components/ui/DrawerSelect";

// Language options with native names and image icons
export const languageOptions: SelectOption[] = [
  {
    id: "zh",
    country: "chinese",
    label: "中文",
    icon: (
      <img
        src="/lang/zh.png"
        alt="Chinese flag"
        className="w-8 h-8 object-contain"
      />
    ),
  },
  {
    id: "en",
    country: "english",
    label: "English",
    icon: (
      <img
        src="/lang/en.png"
        alt="British flag"
        className="w-8 h-8 object-contain"
      />
    ),
  },
  {
    id: "ru",
    label: "Русский",
    country: "russian",
    icon: (
      <img
        src="/lang/ru.png"
        alt="Russian flag"
        className="w-8 h-8 object-contain"
      />
    ),
  },
  {
    id: "vi",
    label: "Tiếng Việt",
    country: "vietnam",
    icon: (
      <img
        src="/lang/vi.png"
        alt="Vietnamese flag"
        className="w-8 h-8 object-contain"
      />
    ),
  },
  {
    id: "hi",
    label: "हिन्दी",
    country: "hindi",
    icon: (
      <img
        src="/lang/hi.png"
        alt="Indian flag"
        className="w-8 h-8 object-contain"
      />
    ),
  },
  {
    id: "es",
    label: "Español",
    country: "spain",
    icon: (
      <img
        src="/lang/es.png"
        alt="Spanish flag"
        className="w-8 h-8 object-contain"
      />
    ),
  },
  {
    id: "hy",
    label: "Հայերեն",
    country: "armenia",
    icon: (
      <img
        src="/lang/hy.png"
        alt="Armenian flag"
        className="w-8 h-8 object-contain"
      />
    ),
  },
];

// Helper function to get language name by ID
export const getLanguageName = (id: string): string => {
  const option = languageOptions.find((opt) => opt.id === id);
  return option?.label || id;
};
