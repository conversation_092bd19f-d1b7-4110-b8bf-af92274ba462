// Define a type for parsed VTT cues
export interface VttCue {
  start: number; // time in seconds
  end: number; // time in seconds
  text: string;
}

// Simple VTT parser function
export const parseVTT = (vttContent: string): VttCue[] => {
  const cues: VttCue[] = [];
  const lines = vttContent.split("\n");
  let currentCue: Partial<VttCue> = {};
  let timeDataFound = false;

  const timeStringToSeconds = (timeStr: string): number => {
    const parts = timeStr.split(":");
    let seconds = 0;
    if (parts.length === 3) {
      // HH:MM:SS.ms
      seconds += parseFloat(parts[0]) * 3600;
      seconds += parseFloat(parts[1]) * 60;
      seconds += parseFloat(parts[2]);
    } else if (parts.length === 2) {
      // MM:SS.ms
      seconds += parseFloat(parts[0]) * 60;
      seconds += parseFloat(parts[1]);
    }
    return seconds;
  };

  for (const line of lines) {
    const trimmedLine = line.trim();
    // Ignore empty lines, WEBVTT header, NOTE, STYLE, REGION
    if (
      !trimmedLine ||
      trimmedLine === "WEBVTT" ||
      trimmedLine.startsWith("NOTE") ||
      trimmedLine.startsWith("STYLE") ||
      trimmedLine.startsWith("REGION")
    ) {
      continue;
    }

    // Detect time data (e.g., "00:00:20.875 --> 00:00:31.800")
    if (trimmedLine.includes("-->")) {
      // If we found time data for a *new* cue, save the previous one (if complete)
      if (
        currentCue.start !== undefined &&
        currentCue.end !== undefined &&
        currentCue.text !== undefined
      ) {
        cues.push(currentCue as VttCue);
      }
      // Start a new cue
      const [startStr, endStr] = trimmedLine.split(" --> ");
      currentCue = {
        start: timeStringToSeconds(startStr),
        end: timeStringToSeconds(endStr.split(" ")[0]), // Ignore potential settings after time
        text: "",
      };
      timeDataFound = true; // We've found the time for the current potential cue
    }
    // Assume lines after time data are cue text, until the next time data or empty line
    else if (timeDataFound) {
      // Append text lines, separated by newline if cue already has text
      currentCue.text =
        (currentCue.text ? currentCue.text + "\n" : "") + trimmedLine;
    }
  }

  // Add the last cue if it's complete
  if (
    currentCue.start !== undefined &&
    currentCue.end !== undefined &&
    currentCue.text !== undefined
  ) {
    cues.push(currentCue as VttCue);
  }

  // console.log("Parsed Cues:", cues);
  return cues;
};

// Helper function to format time (e.g., 0:00)
export const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || seconds === Infinity) {
    return "0:00";
  }
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${minutes}:${secs < 10 ? "0" : ""}${secs}`;
};
