import axios, {
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
} from "axios";

import { languageOptions } from "./languageOptions";
import Cookies from "universal-cookie";

// Base API configuration
const API_CONFIG = {
  // JIKAN_BASE_URL: "https://api.jikan.moe/v4", // Removed Jikan
  // Add other API base URLs as needed
  TEST_APP_BASE_URL: "https://dev.snapdrama.vip/api",
  // TEST_APP_BASE_URL: "https://af15-210-187-164-198.ngrok-free.app/",
  PROD_APP_BASE_URL: "https://snapdrama.vip/api",
  // APP_BASE_URL: "https://a214-210-187-164-198.ngrok-free.app",
};

// Create axios instance with default config
const baseURL =
  process.env.NODE_ENV === "production"
    ? API_CONFIG.PROD_APP_BASE_URL
    : API_CONFIG.TEST_APP_BASE_URL;
console.log(process.env.NODE_ENV);
console.log(baseURL);
const apiClient = axios.create({
  baseURL,
  timeout: 10000, // 10 seconds
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add language headers and other global configurations
apiClient.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  // Get current language from i18next

  // Set the base URL based on the API being called
  // This can be enhanced further by detecting API endpoint patterns
  // if (config.url?.includes("/top/anime") || config.url?.startsWith("/anime")) { // Removed Jikan specific logic
  //   config.baseURL = API_CONFIG.JIKAN_BASE_URL;
  // } else {
  //   config.baseURL = baseURL;
  // }
  // Since Jikan is removed, always use the primary baseURL
  config.baseURL = baseURL;

  return config;
});

// Response interceptor for global error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    // Log errors or perform global error handling
    console.error("API request failed:", error);
    return Promise.reject(error);
  }
);

// Generic request function with typing
export async function request<T>(config: AxiosRequestConfig): Promise<T> {
  try {
    // Debug headers before sending request
    if (config.url === "/home/<USER>") {
      console.log("FINAL REQUEST HEADERS for /home/<USER>", config.headers);
    }
    const response: AxiosResponse<T> = await apiClient(config);
    return response.data;
  } catch (error) {
    console.error("Request failed:", error);
    throw error;
  }
}

// Helper functions for common HTTP methods
export const api = {
  get: <T>(
    url: string,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ) => request<T>({ method: "GET", url, params, ...config }),

  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    request<T>({ method: "POST", url, data, ...config }),

  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    request<T>({ method: "PUT", url, data, ...config }),

  delete: <T>(url: string, config?: AxiosRequestConfig) =>
    request<T>({ method: "DELETE", url, ...config }),
};

// Specific API functions for different endpoints // Removed animeApi

// TypeScript interfaces for the API responses // Removed Jikan specific interfaces
