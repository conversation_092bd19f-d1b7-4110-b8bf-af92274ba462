/**
 * Utility functions for handling channel parameter
 */

/**
 * Get channel parameter from current URL query params and save to localStorage
 * @returns The channel value or undefined if not found
 */
export const getChannelParameter = (): string | undefined => {
  if (typeof window !== "undefined") {
    // First, try to get channel from current URL query parameters
    const urlParams = new URLSearchParams(window.location.search);
    const channelFromUrl = urlParams.get("channel");

    if (channelFromUrl) {
      // Save to localStorage for persistence
      localStorage.setItem("channel", channelFromUrl);
      return channelFromUrl;
    }

    // If no channel in URL, fallback to localStorage
    return localStorage.getItem("channel") || undefined;
  }

  return undefined;
};

/**
 * Set channel parameter in localStorage
 * @param channel The channel value to set
 */
export const setChannelParameter = (channel: string): void => {
  if (typeof window !== "undefined") {
    localStorage.setItem("channel", channel);
  }
};

/**
 * Clear channel parameter from localStorage
 */
export const clearChannelParameter = (): void => {
  if (typeof window !== "undefined") {
    localStorage.removeItem("channel");
  }
};

/**
 * Check if channel parameter exists in current URL and trigger analytics tracking
 * @returns The channel value if found and tracked, undefined otherwise
 */
export const trackChannelVisitIfPresent = (): string | undefined => {
  if (typeof window !== "undefined") {
    const urlParams = new URLSearchParams(window.location.search);
    const channelFromUrl = urlParams.get("channel");

    if (channelFromUrl) {
      // Import analytics here to avoid circular dependencies
      import("~/utils/analytics")
        .then(({ referralEvents }) => {
          referralEvents.channelVisit(channelFromUrl, window.location.pathname);
        })
        .catch((error) => {
          console.error("Error loading analytics module:", error);
        });

      // Also save to localStorage for persistence
      localStorage.setItem("channel", channelFromUrl);
      return channelFromUrl;
    }
  }

  return undefined;
};
