// export const animeApi = {
//     getTopAnime: (limit: number = 25) =>
//       api.get<JikanApiPaginationResponse<JikanAnime>>("/top/anime", { limit }),

import { api } from "./api";
import {
  BaseResponse,
  HomeLangResponse,
  HomeIndexResponse,
  PostDetailsResponse,
  PostListPayload,
  PostListResponse,
  SearchPostPayload,
  SearchPostListResponse,
  RegisterPayload,
  RegisterResponse,
  LoginPayload,
  LoginResponse,
  GetUserInfoResponse,
  SendCodePayload,
  SendCodeResponse,
  ForgotPasswordPayload,
  ForgotPasswordResponse,
  ResetPasswordPayload,
  ResetPasswordResponse,
  UpdatePersonalInfoPayload,
  UpdatePersonalInfoResponse,
  UpdatePasswordPayload,
  UpdatePasswordResponse,
  WatchlistResponse,
  AddToWatchlistPayload,
  AddToWatchlistResponse,
  RemoveFromWatchlistPayload,
  RemoveFromWatchlistResponse,
  UpdateRelPayload,
  UpdateRelResponse,
  PaginationPayload,
  PraiseResponse,
  CollectResponse,
  TopicResponse,
  PayConfigResponse,
  WalletPayPayload,
  WalletPayResponse,
  BuyVipPayload,
  BuyVipResponse,
  WalletEventsPayload,
  WalletEventsResponse,
  BuyPostPayload,
  BuyPostResponse,
} from "~/types/index";
import { languageOptions } from "./languageOptions";
import { getChannelParameter } from "./channel";
import i18next from "i18next";

// Token management utilities
export const TOKEN_KEY = "token";

const getStoredToken = (): string | null => {
  if (typeof window === "undefined") return null;
  return localStorage.getItem(TOKEN_KEY);
};

const setStoredToken = (token: string): void => {
  if (typeof window !== "undefined") {
    localStorage.setItem(TOKEN_KEY, token);
  }
};

const clearStoredToken = (): void => {
  if (typeof window !== "undefined") {
    localStorage.removeItem(TOKEN_KEY);
  }
};

const createAuthHeaders = () => {
  const token = getStoredToken();
  const channel = getChannelParameter();
  const headers: Record<string, string> = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  if (channel) {
    headers.channel = channel;
  }

  return headers;
};

//     getAnimeById: (id: number) => api.get<{ data: JikanAnime }>(`/anime/${id}`),

//     // Add more anime-specific API calls as needed
//   };

// Create a factory function that returns API methods with country and channel headers
export const createBaseApi = (locale?: string) => {
  if (!locale) {
    locale = i18next.language;
  }

  // Get country from locale
  const country = locale
    ? languageOptions.find((option) => option.id === locale)?.country
    : undefined;

  // Get channel parameter
  const channel = getChannelParameter();

  // Default headers with country and channel if available
  const defaultHeaders: Record<string, string> = {};
  if (country) {
    defaultHeaders.country = country;
  }
  if (channel) {
    defaultHeaders.channel = channel;
  }

  // Create wrapper functions that automatically include country and channel headers
  const apiGet = <T>(url: string, params?: Record<string, any>) =>
    api.get<T>(url, params, { headers: defaultHeaders });

  const apiPost = <T>(url: string, data?: any) =>
    api.post<T>(url, data, { headers: defaultHeaders });

  // Helper to create authenticated API calls with country and channel headers
  const createAuthenticatedCall = <T>(endpoint: string, payload?: any) => {
    const token = getStoredToken();
    if (!token) {
      throw new Error("Authentication token is required for this operation");
    }

    return api.post<T>(endpoint, payload, {
      headers: { ...defaultHeaders, ...createAuthHeaders() },
    });
  };

  return {
    getHomePage: () => apiGet("/home"),
    getHomeIndex: () => apiPost<HomeIndexResponse>("/home/<USER>"),
    getHomeLang: () => apiPost<HomeLangResponse>("/home/<USER>"),
    getHomeTopic: () => apiPost<TopicResponse>("/home/<USER>"),
    getPostDetails: (id: string) =>
      apiPost<PostDetailsResponse>("/post/details", { id }),
    getPostList: (payload: PostListPayload) =>
      apiPost<PostListResponse>("/post/list", payload),
    getAuthenticatedPostList: (payload: PostListPayload) =>
      createAuthenticatedCall<PostListResponse>("/post/list", payload),
    searchPosts: (payload: SearchPostPayload) =>
      apiPost<SearchPostListResponse>("/home/<USER>", payload),

    // Authentication endpoints
    register: (payload: RegisterPayload) =>
      apiPost<RegisterResponse>("/auth/register", payload),

    login: (payload: LoginPayload) =>
      apiPost<LoginResponse>("/auth/login", payload).then((response) => {
        if (response.data) {
          setStoredToken(response.data);
        }
        return response;
      }),

    sendCode: (payload: SendCodePayload) =>
      apiPost<SendCodeResponse>("/auth/sendCode", payload),

    getUserInfo: () =>
      createAuthenticatedCall<GetUserInfoResponse>("/auth/getUserInfo"),

    // User account management endpoints
    updatePersonalInfo: (payload: UpdatePersonalInfoPayload) =>
      createAuthenticatedCall<UpdatePersonalInfoResponse>(
        "/auth/updateUserInfo",
        payload
      ),

    updatePassword: (payload: UpdatePasswordPayload) =>
      createAuthenticatedCall<UpdatePasswordResponse>(
        "/auth/updatePassword",
        payload
      ),

    logout: () =>
      createAuthenticatedCall<BaseResponse<any>>("/auth/logout").then(
        (response) => {
          clearStoredToken();
          return response;
        }
      ),

    // Forgot Password endpoints
    forgotPassword: (payload: ForgotPasswordPayload) =>
      apiPost<ForgotPasswordResponse>("/auth/forgotPassword", payload),
    resetPassword: (payload: ResetPasswordPayload) =>
      apiPost<ResetPasswordResponse>("/auth/resetPassword", payload),

    // Payment endpoints
    createOrder: (payload: { amount: number; currency: string }) =>
      createAuthenticatedCall<BaseResponse<string>>(
        "/auth/createOrder",
        payload
      ),

    // Watchlist endpoints
    getWatchlist: () =>
      createAuthenticatedCall<WatchlistResponse>("/auth/getWatchlist"),

    addToWatchlist: (payload: AddToWatchlistPayload) =>
      createAuthenticatedCall<AddToWatchlistResponse>(
        "/auth/addToWatchlist",
        payload
      ),

    removeFromWatchlist: (payload: RemoveFromWatchlistPayload) =>
      createAuthenticatedCall<RemoveFromWatchlistResponse>(
        "/auth/removeFromWatchlist",
        payload
      ),

    clearWatchlist: () =>
      createAuthenticatedCall<BaseResponse<string>>("/auth/clearWatchlist"),

    // Post interaction endpoints
    updateRel: (payload: UpdateRelPayload) =>
      createAuthenticatedCall<UpdateRelResponse>("/post/updateRel", payload),

    // Praise/Liked content endpoints
    getPraise: (payload: PaginationPayload) =>
      createAuthenticatedCall<PraiseResponse>("/home/<USER>", payload),

    // Collect content endpoints
    getCollect: (payload: PaginationPayload) =>
      createAuthenticatedCall<CollectResponse>("/home/<USER>", payload),

    // Payment config endpoint
    getPayConfig: () => apiPost<PayConfigResponse>("/wallet/payConfig"),

    // Order creation endpoints
    walletPay: (payload: WalletPayPayload) =>
      createAuthenticatedCall<WalletPayResponse>("/wallet/pay", payload),
    buyVip: (payload: BuyVipPayload) =>
      createAuthenticatedCall<BuyVipResponse>("/wallet/buyVip", payload),

    // Wallet events endpoint
    getWalletEvents: (payload: WalletEventsPayload) =>
      createAuthenticatedCall<WalletEventsResponse>("/wallet/events", payload),

    // Buy post endpoint for unlocking content with coins
    buyPost: (payload: BuyPostPayload) =>
      createAuthenticatedCall<BuyPostResponse>("/post/buy", payload),
  };
};

/**
 * Default API instance using the current locale
 * This is a convenience export for components that don't need to specify a locale
 */
export const baseApi = createBaseApi();

/**
 * Type definitions for better TypeScript support
 */
export type BaseApiInstance = ReturnType<typeof createBaseApi>;

/**
 * Helper function to check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  return !!getStoredToken();
};

/**
 * Helper function to get current user token
 */
export const getCurrentToken = (): string | null => {
  return getStoredToken();
};
