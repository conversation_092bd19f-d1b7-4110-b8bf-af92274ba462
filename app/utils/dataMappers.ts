/**
 * Centralized Data Mapping Utilities
 *
 * This module provides type-safe data transformation functions for converting
 * various API response types to standardized UI interfaces.
 *
 * Features:
 * - ContentItem mappers for different API response types
 * - VideoData transformations for video-specific data
 * - Episode data transformations
 * - Placeholder URL generation utilities
 * - Type-safe mapping with proper error handling
 */

import type {
  ContentItem,
  HomeIndexListItem,
  SearchPostItem,
  CollectItem,
  PostDetailsData,
  EpisodeItem,
  VideoData,
} from "~/types/index";

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generates a placeholder image URL with encoded title
 */
export const generatePlaceholderUrl = (
  title: string,
  width: number = 192,
  height: number = 108
): string => {
  return `https://www.claudeusercontent.com/api/placeholder/${width}/${height}?name=${encodeURIComponent(
    title
  )}`;
};

/**
 * Safely extracts a numeric value from a string (for episode numbers, etc.)
 */
export const extractNumber = (
  value: string | undefined,
  fallback: number = 0
): number => {
  if (!value) return fallback;
  const match = value.match(/^(\d+)/);
  return match ? parseInt(match[1], 10) : fallback;
};

/**
 * Truncates text to a specified length with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};

// ============================================================================
// CONTENTITEM MAPPERS
// ============================================================================

/**
 * External API item interface for Jikan anime data
 */
export interface ExternalApiItem {
  mal_id: string | number;
  title: string;
  images?: {
    jpg?: {
      large_image_url?: string | null;
      image_url?: string | null;
    };
  };
  score?: number | null;
  aired?: {
    from?: string | null;
  };
}

/**
 * Union type for all supported post item types
 */
export type PostItem =
  | HomeIndexListItem
  | SearchPostItem
  | ExternalApiItem
  | CollectItem;

/**
 * Type guards to identify different post item types
 */
const isHomeIndexListItem = (item: PostItem): item is HomeIndexListItem => {
  return (
    "introduce" in item ||
    "ratings" in item ||
    "size" in item ||
    "topic" in item
  );
};

const isSearchPostItem = (item: PostItem): item is SearchPostItem => {
  return "picture" in item && !("introduce" in item) && !("mal_id" in item);
};

const isExternalApiItem = (item: PostItem): item is ExternalApiItem => {
  return "mal_id" in item;
};

const isCollectItem = (item: PostItem): item is CollectItem => {
  return (
    "contentType" in item ||
    ("introduce" in item && "vip" in item && "gold" in item)
  );
};

/**
 * Unified mapping function for all post item types to ContentItem
 * Replaces mapHomeIndexItemToContentItem, mapSearchPostItemToContentItem, and mapExternalApiItemToContentItem
 *
 * Used in:
 * - app/routes/_index.tsx (HomeIndexListItem)
 * - app/hooks/useDebounceSearch.ts (SearchPostItem)
 * - app/hooks/useGenreFilter.ts (SearchPostItem)
 * - app/routes/trending.tsx (ExternalApiItem)
 * - app/routes/browse.tsx (ExternalApiItem)
 */
export const mapPostItemToContentItem = (item: PostItem): ContentItem => {
  if (isHomeIndexListItem(item)) {
    // HomeIndexListItem mapping (most complete data)
    return {
      id: item.id,
      name: item.title,
      thumbnailUrl: item.picture || generatePlaceholderUrl(item.title),
      description: item.introduce,
      genre: item.topic,
      year: undefined,
      rating: item.ratings,
      duration: undefined,
      episodes: item.size,
    };
  }

  if (isSearchPostItem(item)) {
    // SearchPostItem mapping (basic data)
    return {
      id: item.id,
      name: item.title,
      thumbnailUrl: item.picture || generatePlaceholderUrl(item.title),
      description: undefined,
      genre: undefined,
      year: undefined,
      rating: undefined,
      duration: undefined,
      episodes: undefined,
    };
  }

  if (isExternalApiItem(item)) {
    // ExternalApiItem mapping (Jikan anime data)
    return {
      id: String(item.mal_id),
      name: item.title,
      thumbnailUrl:
        item.images?.jpg?.large_image_url ??
        item.images?.jpg?.image_url ??
        generatePlaceholderUrl(item.title),
      description: undefined,
      genre: undefined,
      year: undefined,
      rating: item.score || undefined,
      duration: undefined,
      episodes: undefined,
    };
  }

  if (isCollectItem(item)) {
    // CollectItem mapping (watchlist/collected content)
    return {
      id: item.id,
      name: item.title,
      thumbnailUrl: item.picture || generatePlaceholderUrl(item.title),
      description: item.introduce,
      genre: item.topic,
      year: undefined,
      rating: undefined,
      duration: undefined,
      episodes: undefined,
    };
  }

  // Fallback for unknown types (should not happen with proper typing)
  throw new Error(`Unsupported post item type: ${JSON.stringify(item)}`);
};

/**
 * Maps PostDetailsData to VideoData
 * Used in: app/routes/videos.play.$videoId.tsx
 */
export const mapPostDetailsToVideoData = (
  details: PostDetailsData
): VideoData => ({
  id: details.id,
  videoUrl: details.playUrl || "",
  posterUrl: details.picture,
  title: details.title,
  description: details.introduce,
  tags: details.topic,
  profileImageUrl: undefined,
  username: undefined,
  initialLikes: undefined,
  initialComments: undefined,
  initialShares: undefined,
  initialLiked: false,
  initialSaved: false,
  seriesTitle: details.contentType === "collection" ? details.title : undefined,
  seasonNumber: undefined,
  currentEpisodeNumber: undefined,
  locked: details.vip,
  contentType: details.contentType,
});

// ============================================================================
// EPISODE MAPPERS
// ============================================================================

/**
 * Episode grid item interface for UI components
 */
export interface EpisodeGridItem {
  number: number;
  locked: boolean;
  active: boolean;
}

/**
 * Maps EpisodeItem array to EpisodeGridItem array
 * Used in: app/components/video-feed-desktop.tsx
 */
export const mapEpisodesToGridItems = (
  episodes: EpisodeItem[],
  activeEpisodeId?: string
): EpisodeGridItem[] => {
  return episodes.map((episode, index) => {
    const parsedNumber = episode.title
      ? parseInt(episode.title, 10)
      : index + 1;
    const episodeNumber = isNaN(parsedNumber) ? index + 1 : parsedNumber;

    return {
      number: episodeNumber,
      locked: episode.vip,
      active: episode.id === activeEpisodeId,
    };
  });
};

/**
 * Sorts episodes by numeric order extracted from title
 * Used in: app/routes/videos.play.$videoId.tsx
 */
export const sortEpisodesByNumber = (
  episodes: EpisodeItem[]
): EpisodeItem[] => {
  return [...episodes].sort((a, b) => {
    const numA = extractNumber(a.title);
    const numB = extractNumber(b.title);
    return numA - numB;
  });
};

/**
 * Gets episode number from title or index
 * Used in: app/components/video/EpisodeGrid.tsx
 */
export const getEpisodeNumber = (
  episode: EpisodeItem,
  fallbackIndex: number = 0
): number => {
  if (episode.title) {
    const match = episode.title.match(/^(\d+)/);
    if (match) {
      return parseInt(match[1], 10);
    }
  }
  return fallbackIndex;
};

// ============================================================================
// BATCH MAPPERS
// ============================================================================

/**
 * Unified function to map an array of any PostItem type to ContentItems
 * Replaces mapHomeIndexItemsToContentItems, mapSearchPostItemsToContentItems, and mapExternalApiItemsToContentItems
 */
export const mapPostItemsToContentItems = (
  items: PostItem[]
): ContentItem[] => {
  return items.map((item) => mapPostItemToContentItem(item));
};

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Validates that a mapped ContentItem has required fields
 */
export const validateContentItem = (item: ContentItem): boolean => {
  return !!(item.id && item.name && item.thumbnailUrl);
};

/**
 * Filters out invalid ContentItems from an array
 */
export const filterValidContentItems = (
  items: ContentItem[]
): ContentItem[] => {
  return items.filter(validateContentItem);
};
