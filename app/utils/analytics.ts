/**
 * Google Analytics event tracking utility
 * This implementation assumes window.gtag is available (GA4)
 */

// Type definitions
interface GaEventProps {
  action: string;
  category?: string;
  label?: string;
  value?: number;
  nonInteraction?: boolean;
  [key: string]: any; // Allow for custom parameters
}

/**
 * Track an event in Google Analytics
 * @param eventProps - Event properties
 */
export const trackEvent = (eventProps: GaEventProps): void => {
  const {
    action,
    category = "Video",
    label,
    value,
    nonInteraction = false,
    ...customParams
  } = eventProps;

  // Make sure we're in a browser environment and gtag is available
  if (typeof window !== "undefined" && typeof window.gtag === "function") {
    window.gtag("event", action, {
      event_category: category,
      event_label: label,
      value,
      non_interaction: nonInteraction,
      ...customParams,
    });
  } else {
    // For development - log events when GA is not available
    console.log("[GA Event]", {
      action,
      category,
      label,
      value,
      nonInteraction,
      ...customParams,
    });
  }
};

/**
 * Video specific events
 */
export const videoEvents = {
  play: (videoId: string, videoTitle: string) =>
    trackEvent({
      action: "video_play",
      label: videoTitle,
      video_id: videoId,
    }),

  pause: (videoId: string, videoTitle: string) =>
    trackEvent({
      action: "video_pause",
      label: videoTitle,
      video_id: videoId,
    }),

  complete: (videoId: string, videoTitle: string) =>
    trackEvent({
      action: "video_complete",
      label: videoTitle,
      video_id: videoId,
    }),

  progress: (videoId: string, videoTitle: string, percentage: number) =>
    trackEvent({
      action: "video_progress",
      label: videoTitle,
      value: Math.floor(percentage),
      video_id: videoId,
      percent: percentage.toFixed(2),
    }),

  seek: (
    videoId: string,
    videoTitle: string,
    fromTime: number,
    toTime: number
  ) =>
    trackEvent({
      action: "video_seek",
      label: videoTitle,
      video_id: videoId,
      from_time: fromTime.toFixed(2),
      to_time: toTime.toFixed(2),
    }),

  like: (videoId: string, videoTitle: string, isLiked: boolean) =>
    trackEvent({
      action: isLiked ? "video_like" : "video_unlike",
      label: videoTitle,
      video_id: videoId,
    }),

  share: (videoId: string, videoTitle: string, platform?: string) =>
    trackEvent({
      action: "video_share",
      label: videoTitle,
      video_id: videoId,
      platform,
    }),

  comment: (videoId: string, videoTitle: string) =>
    trackEvent({
      action: "video_comment",
      label: videoTitle,
      video_id: videoId,
    }),

  changeEpisode: (
    seriesTitle: string,
    fromEpisodeId: number,
    toEpisodeId: number,
    fromEpisode: string,
    toEpisode: string
  ) =>
    trackEvent({
      action: "change_episode",
      category: "Navigation",
      label: seriesTitle,
      from_episode_id: fromEpisodeId,
      to_episode_id: toEpisodeId,
      from_episode: fromEpisode,
      to_episode: toEpisode,
    }),

  changeSeries: (fromSeries: string, toSeries: string) =>
    trackEvent({
      action: "change_series",
      category: "Navigation",
      from_series: fromSeries,
      to_series: toSeries,
    }),

  selectEpisode: (
    videoId: string,
    seriesTitle: string,
    episodeNumber: number,
    episodeId: string
  ) =>
    trackEvent({
      action: "select_episode",
      category: "Video Player",
      label: `Episode ${episodeNumber}`,
      video_id: videoId,
      series_title: seriesTitle || "Unknown Series",
      target_episode_id: episodeId,
    }),
};

/**
 * UI Interaction events
 */
export const uiEvents = {
  closeVideo: (videoId: string, videoTitle: string) =>
    trackEvent({
      action: "close_video",
      category: "Navigation",
      label: videoTitle || "Unknown Video",
      video_id: videoId,
    }),

  languageSelectorOpened: (
    isDesktop: boolean,
    currentLanguage: string,
    currentLanguageName: string
  ) =>
    trackEvent({
      action: "language_selector_opened",
      category: "UI Interaction",
      label: isDesktop ? "Dropdown" : "Drawer",
      current_language: currentLanguage,
      current_language_name: currentLanguageName,
      page_path:
        typeof window !== "undefined" ? window.location.pathname : undefined,
    }),
};

/**
 * Language specific events
 */
export const languageEvents = {
  changeLanguage: (
    previousLanguage: string,
    newLanguage: string,
    previousLanguageName: string,
    newLanguageName: string
  ) =>
    trackEvent({
      action: "change_language",
      category: "Settings",
      label: newLanguageName,
      from_language: previousLanguage,
      to_language: newLanguage,
      from_language_name: previousLanguageName,
      to_language_name: newLanguageName,
      page_path:
        typeof window !== "undefined" ? window.location.pathname : undefined,
      timestamp: new Date().toISOString(),
    }),
};

/**
 * Page view events
 */
export const pageEvents = {
  videoPlayerView: (videoId: string) =>
    trackEvent({
      action: "page_view",
      category: "Page",
      label: "Video Player",
      video_id: videoId,
    }),
};

/**
 * Referral specific events
 */
export const referralEvents = {
  tracePlayReferral: (
    videoId: string,
    traceId: string,
    videoTitle?: string,
    language?: string
  ) => {
    const fullTraceContext = `channel=${traceId} video_id=${videoId} video_title=${videoTitle} language=${language}`;
    console.log("fullTraceContext", fullTraceContext);
    return trackEvent({
      action: "trace_play_referral",

      label: videoTitle || `Video ID: ${videoId}`,
      video_id: videoId,
      channel: traceId,
      video_title: videoTitle,
      language: language,
      full_trace_context: fullTraceContext,
    });
  },

  channelVisit: (channelId: string, currentPage?: string) =>
    trackEvent({
      action: "channel_visit",
      category: "Referral",
      label: `Channel: ${channelId}`,
      channel: channelId,
      page_path:
        currentPage ||
        (typeof window !== "undefined" ? window.location.pathname : undefined),
      timestamp: new Date().toISOString(),
    }),
};

// For TypeScript compatibility with gtag
declare global {
  interface Window {
    gtag: (
      command: string,
      action: string,
      params?: Record<string, any>
    ) => void;
  }
}
