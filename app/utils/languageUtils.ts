import { i18n } from "i18next";
import { languageEvents } from "./analytics";
import { getLanguageName } from "./languageOptions";

import type { NavigateFunction, Location } from "@remix-run/react";

interface ChangeLanguageOptions {
  newLanguage: string;
  i18n: i18n;
  onSuccess?: () => void;
  location?: Location;
  navigate?: NavigateFunction;
}

export const changeLanguage = async ({
  newLanguage,
  i18n,
  onSuccess,
  location,
  navigate,
}: ChangeLanguageOptions) => {
  if (!newLanguage) return;

  try {
    const previousLanguage = i18n.language || DEFAULT_LANGUAGE;

    // Only track analytics if actually changing to a different language
    if (previousLanguage !== newLanguage && typeof window !== "undefined") {
      languageEvents.changeLanguage(
        previousLanguage,
        newLanguage,
        getLanguageName(previousLanguage),
        getLanguageName(newLanguage)
      );
    }

    // Change the language
    await i18n.changeLanguage(newLanguage);

    // Update URL with language parameter if location and navigate are provided
    if (location && navigate) {
      const searchParams = new URLSearchParams(location.search);
      searchParams.set("lng", newLanguage);
      navigate(`${location.pathname}?${searchParams.toString()}`, {
        replace: true,
      });
    }

    // Call success callback if provided
    onSuccess?.();
  } catch (error) {
    console.error("Error changing language:", error);
    // Attempt to change language anyway
    await i18n.changeLanguage(newLanguage);
    // Also attempt to update URL in case of error if possible
    if (location && navigate) {
      const searchParams = new URLSearchParams(location.search);
      searchParams.set("lng", newLanguage);
      navigate(`${location.pathname}?${searchParams.toString()}`, {
        replace: true,
      });
    }
  }
};
