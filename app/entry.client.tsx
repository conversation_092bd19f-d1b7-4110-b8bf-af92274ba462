import { Remix<PERSON>rowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
import { DEFAULT_LANGUAGE, LANGUAGE_STORAGE_KEY, supportedLngs } from "./i18n";
import i18next from "i18next";
import { I18nextProvider, initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";
import { getInitialNamespaces } from "remix-i18next/client";

async function hydrate() {
  await i18next
    .use(initReactI18next) // Tell i18next to use the react-i18next plugin
    .use(LanguageDetector) // Setup a client-side language detector
    .use(Backend) // Setup your backend
    .init({
      fallbackLng: DEFAULT_LANGUAGE,
      supportedLngs,
      interpolation: {
        escapeValue: false,
      },
      detection: {
        order: ["querystring", "cookie", "header"],
        lookupCookie: LANGUAGE_STORAGE_KEY,
        lookupLocalStorage: LANGUAGE_STORAGE_KEY,
        lookupQuerystring: "lng",
        caches: ["cookie"],
      },

      backend: {
        loadPath: "/locales/{{lng}}/{{ns}}.json",
      },
    });

  startTransition(() => {
    hydrateRoot(
      document,
      <I18nextProvider i18n={i18next}>
        <StrictMode>
          <RemixBrowser />
        </StrictMode>
      </I18nextProvider>
    );
  });
}

if (window.requestIdleCallback) {
  window.requestIdleCallback(hydrate);
} else {
  // Safari doesn't support requestIdleCallback
  // https://caniuse.com/requestidlecallback
  window.setTimeout(hydrate, 1);
}
