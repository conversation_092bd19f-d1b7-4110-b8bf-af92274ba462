<!DOCTYPE html>
<html>
<head>
  <title>Simple Stripe Payment</title>
  <script src="https://js.stripe.com/v3/"></script>
</head>
<body>
  <h2>Stripe Payment</h2>
  <form id="payment-form">
    <div id="card-element"></div>
    <button type="submit" id="submit">Pay</button>
    <div id="payment-message"></div>
  </form>

  <script>
    const stripe = Stripe('pk_test_51RR73f4cIGELziOfniTDtk59YinfawcVmsl4nZBm5sj62IjyPn3E3TcNO0gF1Ct6xzAAAeFv6Fe9wR5pczuWJnfp003vsrnyRO');

    async function getClientSecret() {
      const response = await fetch('http://************:8081/auth/createOrder', {
        method: 'POST',
        headers: {
          'Request-Origion': 'Knife4j',
          'Referer': 'http://************:8081/doc.html',
          'User-Agent': 'Mozilla/5.0',
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        mode: 'cors' // May need to be adjusted depending on CORS setup
      });

      const result = await response.json();
      return result.data;
    }

    document.addEventListener('DOMContentLoaded', async () => {
      const clientSecret = await getClientSecret();

      const elements = stripe.elements();
      const cardElement = elements.create('card');
      cardElement.mount('#card-element');

      const form = document.getElementById('payment-form');

      form.addEventListener('submit', async (event) => {
        event.preventDefault();

        const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
          payment_method: {
            card: cardElement
          }
        });

        const messageDiv = document.getElementById('payment-message');
        if (error) {
          messageDiv.textContent = error.message;
        } else if (paymentIntent.status === 'succeeded') {
          messageDiv.textContent = 'Payment successful!';
        }
      });
    });
  </script>
</body>
</html>
