{"name": "shortvideo-front", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "build:android": "npm run build && npx cap sync android", "build:ios": "npm run build && npx cap sync ios", "dev": "remix vite:dev --host", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "preview": "vite preview", "typecheck": "tsc", "dev:ios": "cap run ios --live-reload --port 5173", "start": "PORT=4000 remix-serve build/server/index.js "}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/themes": "^3.2.1", "@remix-run/node": "^2.16.5", "@remix-run/react": "^2.16.5", "@splidejs/react-splide": "^0.7.12", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.2", "hls.js": "^1.6.2", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.1.0", "i18next-fetch-backend": "^6.0.0", "i18next-fs-backend": "^2.4.0", "i18next-http-backend": "^3.0.2", "isbot": "^4", "lucide-react": "^0.511.0", "pretty-cache-header": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.1", "remix-i18next": "^6.4.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-safe-area": "^0.6.0", "universal-cookie": "^8.0.1", "zustand": "^5.0.4"}, "devDependencies": {"@capacitor/cli": "^7.2.0", "@remix-run/dev": "^2.16.5", "@remix-run/serve": "^2.16.6", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}